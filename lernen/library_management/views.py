from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from .controller.library_management_manager import *
from core.controller.user.authentication import *
from core.controller.utils.tracking_events import *
from core.controller.utils.template_manager import *
from core.controller.user.notification_manager import *
from core.controller.client.institute_payment_manager import *
from core.controller.utils.reports_manager import *
from core.controller.utils.authorised_actions import *
from datetime import date
from math import *
from core.controller.utils.authorised_actions import *

MODULE_NAME = 'LIBRARY_MANAGEMENT'

def authorized_user_session(request):
	return authorized_module(request,'LIBRARY_MANAGEMENT')


def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "LIBRARY_MANAGEMENT_MODULE_LOADED"})
		user_login_view = get_user_login_view(request)
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		return render(request, 'library_management/layouts/default-library-layout.html', {'dashboard_theme' : get_user_theme(request), 'app_attributes': app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data' :  payment_status_data, 'bell_notification_count' : bell_notification_count, 'academic_years':academic_years, 'current_session':current_session, 'web_ui_preferences' : web_ui_preferences, 'view_only_access' : view_only_access})

def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'library_management/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session})


def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		return render(request, 'library_management/layouts/dashboard-session-content.html',{'user': user_login_view['user']})

def add_book_details_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		required_detail = get_library_details(user_login_view, institute_unique_code)
		publication_list = required_detail.get('publications',[])
		publisher_list = required_detail.get('publishers',[])
		genre_list = required_detail.get('genres',[])
		vendor_list = required_detail.get('vendors',[])
		author_list = required_detail.get('authors',[])
		library_type_list = required_detail.get('libraryTypes',[])
		library_preferences= get_library_preferences(user_login_view, institute_unique_code)
		accession_number_access = library_preferences['enableAccessionNumber']
		accession_number_auto_increment = library_preferences['accessionNumberCounter']
		return render(request, 'library_management/add_book_details/main_screen.html',{'publicationDetails': publication_list, 'publisherDetails': publisher_list, 'genreDetails': genre_list, 'vendorDetails': vendor_list, 'authorDetails': author_list, 'libraryTypeDetails': library_type_list, 'accessionNumberAccess' : accession_number_access, 'accessionNumberAutoIncrement' : accession_number_auto_increment})

def update_book_details_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		required_detail = get_library_details(user_login_view, institute_unique_code)
		publication_list = required_detail.get('publications',[])
		publisher_list = required_detail.get('publishers',[])
		genre_list = required_detail.get('genres',[])
		vendor_list = required_detail.get('vendors',[])
		author_list = required_detail.get('authors',[])
		library_type_list = required_detail.get('libraryTypes',[])
		library_preferences= get_library_preferences(user_login_view, institute_unique_code)
		accession_number_access = library_preferences['enableAccessionNumber']
		accession_number_auto_increment = library_preferences['accessionNumberCounter']
		return render(request, 'library_management/update_book_details/main_screen.html',{'user': user_login_view['user'], 'academic_years':academic_years, 'current_session':current_session, 'publicationDetails': publication_list, 'publisherDetails': publisher_list, 'genreDetails': genre_list, 'vendorDetails': vendor_list, 'authorDetails': author_list, 'libraryTypeDetails': library_type_list, 'accessionNumberAccess' : accession_number_access, 'accessionNumberAutoIncrement' : accession_number_auto_increment})

def get_library_configuration_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		library_details = get_library_type_details(user_login_view, institute_unique_code)
		return render(request, 'library_management/configuration/library_type_list.html',{"libraryDetails" : library_details})

def get_publication_details_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		publication_details = get_publication_details(user_login_view, institute_unique_code)
		return render(request, 'library_management/configuration/publication_list.html',{"publicationDetails" : publication_details})

def get_publisher_details_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		publisher_details = get_publisher_details(user_login_view, institute_unique_code)
		return render(request, 'library_management/configuration/publisher_list.html',{"publisherDetails" : publisher_details})

def get_author_details_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		author_details = get_author_details(user_login_view, institute_unique_code)
		return render(request, 'library_management/configuration/author_list.html',{"authorDetails" : author_details})

def get_vendor_details_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		vendor_details = get_vendor_details(user_login_view, institute_unique_code)
		return render(request, 'library_management/configuration/vendor_list.html',{"vendorDetails" : vendor_details})

def get_genre_details_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		genre_details = get_genre_details(user_login_view, institute_unique_code)
		return render(request, 'library_management/configuration/genre_list.html',{"genreDetails" : genre_details})

def get_book_details_view(request, institute_unique_code, offset, limit):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		book_list_with_pagination_info = get_book_details(user_login_view, institute_unique_code, request.GET.get("text",""), offset, limit)
		pagination_info = book_list_with_pagination_info['paginationInfo']
		pagination_details = get_pagination_details(pagination_info) 
		return render(request, 'library_management/update_book_details/book_details.html',{'bookDetails': book_list_with_pagination_info['result'],  "pagination_details":pagination_details})

@csrf_exempt
def delete_book_details_view(request, institute_unique_code, book_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_book_details(user_login_view, institute_unique_code, book_id)
		return render(request, 'library_management/update_book_details/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_issued_book_details_view(request, institute_unique_code, academic_session_id, transaction_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_issued_book_details(user_login_view, institute_unique_code, transaction_id, academic_session_id)
		return render(request, 'library_management/issue_book/status_modal.html',{"data":response_data})

@csrf_exempt
def update_issued_book_details_view(request, institute_unique_code, academic_session_id, transaction_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		status = request.GET.get('status',"")
		updateBookDate = request.GET.get('update_issue_book_date',"")
		response_data = update_issued_book_details(user_login_view, institute_unique_code, transaction_id, academic_session_id, status, updateBookDate)
		return render(request, 'library_management/issue_book/status_modal.html',{"data":response_data})

@csrf_exempt
def add_new_book_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		document_name = None
		if 'documentName' in request.POST:
			document_name = request.POST['documentName']
		document = None
		if 'document' in request.FILES:
			document = request.FILES['document']
		add_book_details = json.loads(request.POST['add_book_details'])

		response_data = add_new_book(user_login_view, institute_unique_code, add_book_details, document_name, document)
		return render(request, 'library_management/add_book_details/status_modal.html',{"data":response_data})

@csrf_exempt
def update_book_details(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)

		update_book_details = json.loads(request.body)

		response_data = update_book(user_login_view, institute_unique_code, update_book_details)
		return render(request, 'library_management/update_book_details/status_modal.html',{"data":response_data})
	
@csrf_exempt
def update_number_of_book_details(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)

		update_copies_payload = json.loads(request.body)

		response_data = update_number_of_books(user_login_view, institute_unique_code, update_copies_payload)
		return render(request, 'library_management/update_book_details/status_modal.html',{"data":response_data})
	
def document_download_view(request, institute_unique_code, document_id, book_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		document = download_document(user_login_view, institute_unique_code, document_id, book_id)
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/octet-stream")
			response['Content-Disposition'] = 'inline; filename='+document['file_name']
			return response

@csrf_exempt
def document_delete_view(request, institute_unique_code, document_id, book_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response = delete_document(user_login_view, institute_unique_code, document_id, book_id)
		return render(request, 'library_management/manage_book_images/manage_book_image_status_modal.html',{'response' : response})

@csrf_exempt
def document_upload_view(request, institute_unique_code, book_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST' and request.FILES['document']:
		user_login_view = get_user_login_view(request)
		document = request.FILES['document']
		document_type = request.POST['documentType']
		document_name = request.POST['documentName']
		response = upload_document(user_login_view, institute_unique_code,  document_type, document_name, document, book_id)
		return render(request, 'library_management/manage_book_images/manage_book_image_status_modal.html',{'response' : response})

def book_assignment_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		standards = get_standards(user_login_view, institute_unique_code, current_session['academicSessionId'])
		return render(request, 'library_management/issue_book/main_screen.html',{'academic_years':academic_years,'current_session':current_session,'standards':standards})

def student_live_search_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		status = request.GET.get("status","")
		if(status == 'undefined'):
			students = lite_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""))
		else:
			students = lite_search_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("searchText",""), "", request.GET.get("status",""))
		return render(request, 'core/student/live_search_student_list.html',{"students":students})
	
def get_book_details_assigned_view(request, institute_unique_code, session_id, entity, student_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		offset = request.GET.get("offset","")
		limit = request.GET.get("limit","")
		status = request.GET.get("status","")
		student = None
		if(entity == "STUDENT"):
			student = get_student(user_login_view, institute_unique_code, session_id, student_id)
			if student is None:
				return render(request, 'library_management/issue_book/book_assign_list.html',{})
			else:
				student_id = student['studentId']
		student_book_list_with_pagination_info = get_user_book_details(user_login_view, institute_unique_code, session_id, student_id, status, offset, limit)
		pagination_info = student_book_list_with_pagination_info['paginationInfo']
		pagination_details = get_pagination_details(pagination_info)

		return render(request, 'library_management/issue_book/book_assign_list.html',{'student_book_list' : student_book_list_with_pagination_info, 'student' : student, "pagination_details":pagination_details})
	
def get_live_book_search_details_view(request, institute_unique_code):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		book_list = get_search_book_details(user_login_view, institute_unique_code, request.GET.get("searchText",""))
		library_preferences= get_library_preferences(user_login_view, institute_unique_code)
		accession_number_access = library_preferences['enableAccessionNumber']
		return render(request, 'library_management/live_search_book_list.html',{'bookDetails': book_list, 'accession_number_access': accession_number_access})
	
@csrf_exempt
def issue_book_details(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		issue_book_details = json.loads(request.body)

		response_data = issue_book(user_login_view, institute_unique_code, academic_session_id, issue_book_details)
		return render(request, 'library_management/issue_book/status_modal.html',{"data":response_data})
	
@csrf_exempt
def return_book_details(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		return_book_details = json.loads(request.body)

		response_data = return_book(user_login_view, institute_unique_code, return_book_details)
		return render(request, 'library_management/issue_book/status_modal.html',{"data":response_data})

@csrf_exempt
def add_publication_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		publication_payload = json.loads(request.POST['publicationPayload'])

		response_data = add_publication_details(user_login_view, institute_unique_code, publication_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def update_publication_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		publication_payload = json.loads(request.POST['publicationPayload'])

		response_data = update_publication_details(user_login_view, institute_unique_code, publication_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_publication_details_view(request, institute_unique_code, publication_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_publication_details(user_login_view, institute_unique_code, publication_id)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def add_publisher_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		publisher_payload = json.loads(request.POST['publisherPayload'])

		response_data = add_publisher_details(user_login_view, institute_unique_code, publisher_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def update_publisher_details_view(request, institute_unique_code):	
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		publisher_payload = json.loads(request.POST['publisherPayload'])

		response_data = update_publisher_details(user_login_view, institute_unique_code, publisher_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_publisher_details_view(request, institute_unique_code, publisher_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_publisher_details(user_login_view, institute_unique_code, publisher_id)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def add_author_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		author_payload = json.loads(request.POST['authorPayload'])

		response_data = add_author_details(user_login_view, institute_unique_code, author_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def update_author_details_view(request, institute_unique_code):	
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		author_payload = json.loads(request.POST['authorPayload'])

		response_data = update_author_details(user_login_view, institute_unique_code, author_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_author_details_view(request, institute_unique_code, author_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_author_details(user_login_view, institute_unique_code, author_id)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def add_vendor_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		vendor_payload = json.loads(request.POST['vendorPayload'])

		response_data = add_vendor_details(user_login_view, institute_unique_code, vendor_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def update_vendor_details_view(request, institute_unique_code):	
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':	
		user_login_view = get_user_login_view(request)
		vendor_payload = json.loads(request.POST['vendorPayload'])

		response_data = update_vendor_details(user_login_view, institute_unique_code, vendor_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_vendor_details_view(request, institute_unique_code, vendor_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_vendor_details(user_login_view, institute_unique_code, vendor_id)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def add_genre_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		genre_payload = json.loads(request.POST['genrePayload'])

		response_data = add_genre_details(user_login_view, institute_unique_code, genre_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def update_genre_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':	
		user_login_view = get_user_login_view(request)
		genre_payload = json.loads(request.POST['genrePayload'])

		response_data = update_genre_details(user_login_view, institute_unique_code, genre_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_genre_details_view(request, institute_unique_code, genre_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_genre_details(user_login_view, institute_unique_code, genre_id)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def add_library_type_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		library_type_payload = json.loads(request.POST['libraryTypePayload'])

		response_data = add_library_type_details(user_login_view, institute_unique_code, library_type_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def update_library_type_details_view(request, institute_unique_code):	
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':	
		user_login_view = get_user_login_view(request)
		library_type_payload = json.loads(request.POST['libraryTypePayload'])

		response_data = update_library_type_details(user_login_view, institute_unique_code, library_type_payload)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})

@csrf_exempt
def delete_library_type_details_view(request, institute_unique_code, library_type_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':	
		user_login_view = get_user_login_view(request)
		response_data = delete_library_type_details(user_login_view, institute_unique_code, library_type_id)
		return render(request, 'library_management/configuration/status_modal.html',{"data":response_data})
