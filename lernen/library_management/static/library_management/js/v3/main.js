var BOOK_COVER_IMAGE_SIZE_LIMIT = 500;
var STUDENT_ENTITY = "STUDENT";
var STUDENT_ASSIGNED_BOOK_LIST = "student-book-assign-list";

$(document).ready(function() {
    menuLoader.registerSidebarMenu();
    homePage.initHomePage();
    paymentReminder.readPaymentState();
  });

var menuLoader = {

    registerSidebarMenu : function () {
        sideBarHoverEventCallback();
        activateMenuItem();
        menuLoader.registerHomeMenu();
        menuLoader.addBookDetailsMenu();
        menuLoader.updateBookDetailsMenu();
        menuLoader.issueBookDetailsMenu();
        menuLoader.libraryTypeDetailsMenu();
        menuLoader.publicationDetailsMenu();
        menuLoader.publisherDetailsMenu();
        menuLoader.authorDetailsMenu();
        menuLoader.vendorDetailsMenu();
        menuLoader.genreDetailsMenu();
    },
  
    registerHomeMenu : function () {
        $('#homeNav').on('click', function() {
            homePage.loadHomePage();
        });
    },

    addBookDetailsMenu : function () {
        $('#addBookNav').on('click', function() {
            manageBook.loadAddBookDetailsHome("");
        });
    },
  
    updateBookDetailsMenu : function () {
        $('#updateBookNav').on('click', function() {
            manageBook.loadUpdateBookDetailsHome("");
        });
    },

    issueBookDetailsMenu : function () {
        $('#issueBookNav').on('click', function() {
            issueBookDetailsMenu.loadMainScreen("");
        });
    },
    libraryTypeDetailsMenu : function () {
        $('#libraryTypeNav').on('click', function() {
            libraryTypeDetails.loadLibraryTypeDetailsHome();
        });
    },
    publicationDetailsMenu : function () {
        $('#publicationNav').on('click', function() {
            publicationDetails.loadPublicationDetailsHome();
        });
    },
    publisherDetailsMenu : function () {
        $('#publisherNav').on('click', function() {
            publisherDetails.loadPublisherDetailsHome();
        });
    },
    authorDetailsMenu : function () {
        $('#authorNav').on('click', function() {
            authorDetails.loadAuthorDetailsHome();
        });
    },
    vendorDetailsMenu : function () {
        $('#vendorNav').on('click', function() {
            vendorDetails.loadVendorDetailsHome();
        });
    },
    genreDetailsMenu : function () {
        $('#genreNav').on('click', function (){
            genreDetails.loadGenreDetailsHome();
        });
    }
  };
var homePage =  {
      initHomePage : function () {
          academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
  
      },
  
      loadHomePage : function () {
        ajaxClient.get("/library-management/home", function(data) {
            $("#main-content").html(data);
            academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
        });
      },
  
      loadHomePageForSession : function () {
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        ajaxClient.get("/library-management/session-home/"+academicSessionId, function(data) {
            $("#appointment-dashboard-session-content").html(data);
        });
      },
  
      refreshHomePage : function () {
          homePage.loadHomePageForSession();
      },
  
};

var manageBook={
    
    loadAddBookDetailsHome : function (){
        ajaxClient.get("/library-management/book-view", function(data){
            $("#main-content").html(data);
            initDate(365*100);
            manageBook.loadSingleBookTable();
        })
    },

    loadSingleBookTable : function(){
        $('#book-copies').on('change', function () {
        var access = $(".library-accession-number-access").text().trim();
        var numberOfCopies = $('#book-copies').val();
        if(access == "true"){
            manageBook.populateSingleBookTable(".individualBookDetails", {}, numberOfCopies, false);
            $('.row-checkbox-div').css('display' ,'none');
            $('.row-checkbox').css('display' ,'none');
            $('.select-all-checkbox').css('display','none');
        }
        });
    },

    populateSingleBookTable: function(entityParentContainerId, individualBookDetails, numberOfCopies, disabled) {
            $("#copies-error").text("");
        if(numberOfCopies<=0){
            $("#copies-error").text("Need greater than Zero value");
            return
        }
        // Retrieve vendor and library type details from hidden divs
        var vendorDetails = [];
        var libraryTypeDetails = [];
        var jsonVendorDetails = $(".vendor-details").text().trim();
        var jsonLibraryTypeDetails = $(".library-type-details").text().trim();
        if(jsonVendorDetails){
            vendorDetails = JSON.parse(jsonVendorDetails);
        }
        if(jsonLibraryTypeDetails){
            libraryTypeDetails = JSON.parse(jsonLibraryTypeDetails);
        }
        
        // Start table generation with header and autofill row
        var tableHtml = manageBook.generateTableHeader();
        tableHtml += manageBook.generateAutofillRow(vendorDetails, libraryTypeDetails, disabled);
        tableHtml += manageBook.generateTableFooter();
    
        // Generate rows based on the number of copies
        for (var j = 0; j < numberOfCopies; j++) {
            if(Object.keys(individualBookDetails).length === 0){
                tableHtml += manageBook.generateRow(individualBookDetails, vendorDetails, libraryTypeDetails, disabled);
            }else{
                tableHtml += manageBook.generateRow(individualBookDetails[j], vendorDetails, libraryTypeDetails, disabled);
            }
        }
    
        tableHtml += "</tbody></table>";
    
        // Add the generated table HTML to the container
        $(entityParentContainerId).html(tableHtml);
        // Initialize datepickers or other relevant functions
        initDate(365 * 100);
    },
    generateTableHeader : function(){
        return "<table id=\"single-book-table\" class=\"table\" style=\"table-layout: fixed; width: 200%\">" +
        "<thead>" +
        "<tr>" +
            "<th style=\"width: 8%; text-align: center;\" class=\"row-checkbox-div\"></th>" +
            "<th style=\"width: 12%; text-align: center;\">Accession Number*</th>" +
            "<th style=\"width: 8%; text-align: center;\">Rack</th>" +
            "<th style=\"width: 10%; text-align: center;\">Purchase Price</th>" +
            "<th style=\"width: 8%; text-align: center;\">MRP</th>" +
            "<th style=\"width: 10%; text-align: center;\">Bill Number</th>" +
            "<th style=\"width: 12%; text-align: center;\">Date Of Purchase</th>" +
            "<th style=\"width: 10%; text-align: center;\">Vendor</th>" +
            "<th style=\"width: 10%; text-align: center;\">Library Type</th>" +
            "<th style=\"width: 8%; text-align: center;\">Volume</th>" +
            "<th style=\"width: 12%; text-align: center;\">Added On</th>" +
            "<th style=\"width: 8%; text-align: center;\">Status</th>" +
            "<th style=\"width: 14%; text-align: center;\">Remarks</th>" +
        "</tr>" ;
    },             
     
    generateTableFooter : function(){
       return "</thead>" +
              "<tbody>";
    },

    generateVendorOption : function(vendorList, vendorId){
        var vendorOptions = "<option value=\"\">Select Vendor</option>";
        if(vendorList.length > 0){
            vendorList.forEach(function (vendor) {
                var selected = (vendorId === vendor.vendorId) ? "selected" : "";
                vendorOptions += "<option value=\"" + vendor.vendorId + "\" " + selected + ">" + vendor.vendorName + "</option>";
            });
        }
        return vendorOptions;
    },
    generateLibraryTypeOption : function(libraryTypeList, libraryTypeId){
        var libraryTypeOptions = "<option value=\"\">Select Library Type</option>";
        if(libraryTypeList.length > 0){
            libraryTypeList.forEach(function (libraryType) {
                var selected = (libraryTypeId === libraryType.libraryTypeId) ? "selected" : "";
                libraryTypeOptions += "<option value=\"" + libraryType.libraryTypeId + "\" " + selected + ">" + libraryType.libraryName + "</option>";
            });
        }
        return libraryTypeOptions;
    },
    generateRow: function (individualBookDetail, vendorList, libraryTypeList, viewOnly) {
        var disabled = viewOnly ? "disabled" : "";

        if(individualBookDetail.dateOfPurchase != null){
            // Timestamp in seconds
            const dateOfPurchase = individualBookDetail.dateOfPurchase;

            // Convert to a JavaScript Date object
            const purchaseDate = new Date(dateOfPurchase * 1000);

            // Format the date as DD-MMM-YYYY
            const options = { day: '2-digit', month: 'short', year: 'numeric' };
            individualBookDetail.dateOfPurchase = purchaseDate.toLocaleDateString('en-GB', options).replace(',', ' ');

        }
        if(individualBookDetail.addedOn != null){
            // Timestamp in seconds
            const addedOn = individualBookDetail.addedOn;

            // Convert to a JavaScript Date object
            const addedOnDate = new Date(addedOn * 1000);

            // Format the date as DD-MMM-YYYY
            const options = { day: '2-digit', month: 'short', year: 'numeric' };
            individualBookDetail.addedOn = addedOnDate.toLocaleDateString('en-GB', options).replace(',', ' ');

        }
    
        // Generate vendor options
        var vendorId = individualBookDetail.vendor != null ? individualBookDetail.vendor.vendorId : "";
        var vendorOptions = manageBook.generateVendorOption(vendorList, vendorId);

        // Generate library type options
        var libraryTypeId = individualBookDetail.libraryType != null ? individualBookDetail.libraryType.libraryTypeId : "";
        var libraryTypeOptions = manageBook.generateLibraryTypeOption(libraryTypeList, libraryTypeId);

        return "<tr class=\"book-details-row\">" +
            "    <td style=\"display: none;\">" + 
            "      <p class=\"accessionId\" style=\"display: none;\">"+  (individualBookDetail.accessionId || '') +"</p>" + 
            "    </td>" +
            "    <td class=\"row-checkbox-div\">"+
            "       <input type=\"checkbox\" class=\"row-checkbox\">"+
            "    </td>"+
            "    <td>" + 
            "        <input type=\"text\" class=\"form-control accession-number mandatory-field\" value=\"" + (individualBookDetail.accessionNumber || '') + "\" placeholder=\"accession number\" " + disabled + ">" + 
            "    </td>" +
            "    <td>" +
            "        <input type=\"text\" class=\"form-control book-rack\" value=\"" + (individualBookDetail.rack || '') + "\" placeholder=\"Rack\" " + disabled + ">" +
            "    </td>" +
            "    <td>" +
            "        <input type=\"number\" class=\"form-control book-price\" value=\"" + (individualBookDetail.purchagePrice || '') + "\" placeholder=\"Price\" " + disabled + ">" +
            "    </td>" +
            "    <td>" +
            "        <input type=\"number\" class=\"form-control book-mrp\" value=\"" + (individualBookDetail.mrp || '') + "\" placeholder=\"Mrp\" " + disabled + ">" +
            "    </td>" +
            "    <td>" +
            "        <input type=\"text\" class=\"form-control book-bill-number\" value=\"" + (individualBookDetail.billNumber || '') + "\" placeholder=\"Bill Number\" " + disabled + ">" +
            "    </td>" +
            "    <td>" +
            "        <input type=\"text\" class=\"form-control select-date book-purchase-date\" value=\"" + (individualBookDetail.dateOfPurchase|| '') + "\" placeholder=\"Purchase Date\" " + disabled + ">" +
            "    </td>" +
            "    <td>" +
            "        <select class=\"custom-select mr-sm-4 book-vendor\" id=\"book-vendor\" " + disabled + ">" +
            vendorOptions +
            "        </select>" +
            "    </td>" +
            "    <td>" +
            "        <select class=\"custom-select mr-sm-4 book-library-type\" id=\"book-library-type\" " + disabled + ">" +
            libraryTypeOptions +
            "        </select>" +
            "    </td>" +
            "    <td>" +
            "        <input type=\"text\" class=\"form-control book-volume\" value=\"" + (individualBookDetail.volume || '') + "\" placeholder=\"Volume\" " + disabled + ">" +
            "    </td>" +
            "    <td>" +
            "        <input type=\"text\" class=\"form-control select-date book-added-on-date\" value=\"" + (individualBookDetail.addedOn|| '') + "\" placeholder=\"Added On Date\" " + disabled + ">" +
            "    </td>" +
            "    <td>" +
            "        <select class=\"custom-select book-status\" " + disabled + ">" +
            "            <option value=\"ACTIVE\"" + (individualBookDetail.status === 'ACTIVE' ? ' selected' : '') + ">ACTIVE</option>" +
            "            <option value=\"INACTIVE\"" + (individualBookDetail.status === 'INACTIVE' ? ' selected' : '') + ">INACTIVE</option>" +
            "        </select>" +
            "    </td>" +
            "    <td>" +
            "        <textarea class=\"form-control book-remarks\" placeholder=\"Remarks\" " + disabled + ">" + (individualBookDetail.remarks || '') + "</textarea>" +
            "    </td>" +
            "</tr>";
    },    
    generateAutofillRow: function(vendorList, libraryTypeList, viewOnly) {
        var disabled = viewOnly ? "disabled" : "";
        // Generate Vendor Options
        var vendorOptions = manageBook.generateVendorOption(vendorList, "");
    
        // Generate Library Type Options
        var libraryTypeOptions = manageBook.generateLibraryTypeOption(libraryTypeList, "");
        // Return Autofill Row HTML
        return "<tr class=\"autofill-row\">" +
            "    <td class=\"select-all-checkbox\"> "+
            "        <input type=\"checkbox\" class=\"select-all-checkbox\" onclick=\"manageBook.selectAllRows(this)\">"+
            "    </td> "+
            "    <td style=\"text-align: center; vertical-align: middle;\">" +
            "        <button id=\"autofill-button\" class=\"btn btn-outline-success btn-md\" style=\"position: relative; float: center; margin-left: 0;\" onclick=\"manageBook.autofillFields()\"" + disabled + ">Autofill</button>" +
            "    </td>" +
            "    <td class=\"form-group\" style=\"text-align: center; vertical-align: middle;\">" +
            "        <input type=\"text\" class=\"form-control default-rack\" placeholder=\"Rack\"" + disabled + ">" +
            "    </td>" +
            "    <td class=\"form-group\" style=\"text-align: center; vertical-align: middle;\">" +
            "        <input type=\"number\" class=\"form-control default-price\" placeholder=\"Purchage Price\"" + disabled + ">" +
            "    </td>" +
            "    <td class=\"form-group\" style=\"text-align: center; vertical-align: middle;\">" +
            "        <input type=\"number\" class=\"form-control default-mrp\" placeholder=\"Mrp\"" + disabled + ">" +
            "    </td>" +
            "    <td class=\"form-group\" style=\"text-align: center; vertical-align: middle;\">" +
            "        <input type=\"text\" class=\"form-control default-bill-number\" placeholder=\"Bill Number\"" + disabled + ">" +
            "    </td>" +
            "    <td class=\"form-group\" style=\"text-align: center; vertical-align: middle;\">" +
            "        <input type=\"text\" class=\"form-control select-date default-purchase-date\" placeholder=\"Purchase Date\"" + disabled + ">" +
            "    </td>" +
            "    <td class=\"form-group\" style=\"text-align: center; vertical-align: middle;\">" +
            "        <select class=\"custom-select default-vendor\" " + disabled + ">" +
            vendorOptions +
            "        </select>" +
            "    </td>" +
            "    <td class=\"form-group\" style=\"text-align: center; vertical-align: middle;\">" +
            "        <select class=\"custom-select default-library-type\" " + disabled + ">" +
            libraryTypeOptions +
            "        </select>" +
            "    </td>" +
            "    <td style=\"text-align: center; vertical-align: middle;\">" +
            "        <input type=\"text\" class=\"form-control default-volume\" placeholder=\"Volume\" " + disabled + ">" +
            "    </td>" +
            "    <td class=\"form-group\" style=\"text-align: center; vertical-align: middle;\">" +
            "        <input type=\"text\" class=\"form-control select-date default-added-on-date\" placeholder=\"added On Date\"" + disabled + ">" +
            "    </td>" +
            "    <td style=\"text-align: center; vertical-align: middle;\">" +
            "        <select class=\"custom-select default-status\" " + disabled + ">" +
            "            <option value=\"ACTIVE\" selected>ACTIVE</option>" +
            "            <option value=\"INACTIVE\">INACTIVE</option>" +
            "        </select>" +
            "    </td>" +
            "    <td>" +
            "        <textarea class=\"form-control default-remarks\" placeholder=\"Remarks\" " + disabled + "></textarea>" +
            "    </td>" +
            "</tr>";
    },
    selectAllRows: function (checkbox) {
        var isChecked = checkbox.checked;
        $('.row-checkbox').forEach(function (rowCheckbox) {
            rowCheckbox.checked = isChecked;
        });
    },
    autofillFields: function() {
        var defaultRack = $('.default-rack').val();
        var defaultPrice = $('.default-price').val();
        var defaultMrp = $('.default-mrp').val();
        var defaultBillNumber = $('.default-bill-number').val();
        var defaultPurchaseDate = $('.default-purchase-date').val();
        var defaultVendor = $('.default-vendor').val();
        var defaultLibraryType = $('.default-library-type').val();
        var defaultVolume = $('.default-volume').val();
        var defaultAddedOnDate = $('.default-added-on-date').val();
        var defaultStatus = $('.default-status').val();
        var defaultRemarks = $('.default-remarks').val();
    
        $('.book-details-row').each(function() {
            var row = $(this);
            if (defaultRack) row.find('.book-rack').val(defaultRack);
            if (defaultPrice) row.find('.book-price').val(defaultPrice);
            if (defaultMrp) row.find('.book-mrp').val(defaultMrp);
            if (defaultBillNumber) row.find('.book-bill-number').val(defaultBillNumber);
            if (defaultPurchaseDate) row.find('.book-purchase-date').val(defaultPurchaseDate);
            if (defaultVendor) row.find('.book-vendor').val(defaultVendor);
            if (defaultLibraryType) row.find('.book-library-type').val(defaultLibraryType);
            if (defaultVolume) row.find('.book-volume').val(defaultVolume);
            if (defaultAddedOnDate) row.find('.book-added-on-date').val(defaultAddedOnDate);
            if (defaultStatus) row.find('.book-status').val(defaultStatus);
            if (defaultRemarks) row.find('.book-remarks').val(defaultRemarks);
        });
    },            
    createBook : function(){
        var invalid = validateMandatoryFields($("#add\\.book-info-content"));
        if(invalid){
          showErrorDialogBox("Invalid book information. Please try again.")
          return;
        }
        var documentName = "BOOK_COVER_IMAGE";
        var file = "";

        var ONE_KB = 1024;
        if (($("#book-cover-image"))[0].files.length > 0) {
           file = ($("#book-cover-image"))[0].files[0];
             if((file.size / ONE_KB) > BOOK_COVER_IMAGE_SIZE_LIMIT){
               showErrorDialogBox("Size Of document cannot be greater than "+BOOK_COVER_IMAGE_SIZE_LIMIT+" kb");
               return;
            }
        }
        var individualBookDetailList = [];
        var access = $(".library-accession-number-access").text().trim();
        if(access == "true"){
            var returnData = manageBook.returnIndividualBookDetailsListWithAccess(null);
            if(returnData == false){
                return;
            }
            individualBookDetailList = returnData;
        }
        else{
            var returnData = manageBook.returnIndividualBookDetailsListWithOutAccess(null, null, null);
            if(returnData == false)
            {
                return;
            }
            individualBookDetailList.push(returnData);
        }

        var bookTitle = $("#book-title").val().trim();
        var bookNo = $("#book-number").val().trim();
        var genreId = $("#book-genre").val().trim();
        var authorId = $("#book-author").val().trim();
        var isbnNumber = $("#book-isbn-number").val().trim();
        var publicationId = $("#book-publication").val().trim();
        var publisherId = $("#book-publisher").val().trim();
        var edition = $("#book-edition").val().trim();
        var noOfCopies = $("#book-copies").val().trim();
        var publicationYear = $("#book-publication-year").val().trim();
        var language = $("#book-language").val().trim();
        var numberOfPages = $("#book-pages").val().trim();
        var typeOfBinding = $("#book-binding").val().trim();
       
        var tags = $("#book-tags").val().trim();
        var tagsSet = [];

        if(noOfCopies<=0){
            $("#copies-error").text("Need greater than Zero value");
            return;
        }

        if(bookNo == ""){
            bookNo = null;
        }
        if(genreId == ""){
            genreId = null;
        }
        if(authorId == ""){
            authorId = null;
        }
        if(publicationId == ""){
            publicationId = null;
        }
        if(publisherId == ""){
            publisherId = null;
        }
        if(edition == ""){
            edition = null;
        }
        if(publicationYear == ""){
            publicationYear = null;
        }
        if(language == ""){
            language = null;
        }
        if(typeOfBinding == ""){
            typeOfBinding = null;
        }
        if(numberOfPages == ""){
            numberOfPages = null;
        }
        if(tags == ""){
            tags = null;
        }
        var bookDetails = {'bookId': null, 'genreId': genreId, 'bookTitle': bookTitle, 'bookNo': bookNo, 'authorId' : authorId, 'isbn' : isbnNumber, 'publicationId' : publicationId, 'publisherId': publisherId, 'edition' : edition, 'noOfCopies' : noOfCopies, 'publishYear': publicationYear, 'language' : language, 'typeOfBinding': typeOfBinding, 'numberOfPages' : numberOfPages}
        if (tags && tags.includes(',')) {
            var tagsArray = tags.split(',').map(tag => tag.trim());
            tagsSet = new Set(tagsArray);
        }else if(tags){
            tagsSet = new Set([tags.trim()]);
        }

        var addBookPayload = {'bookDetailPayload': bookDetails, 'tags': Array.from(tagsSet), 'individualBookDetailsPayloads': individualBookDetailList}
        var formData = new FormData();

        if(file != ""){
            formData.append('document', file);
            formData.append('documentName', documentName);
        }

        formData.append('add_book_details', JSON.stringify(addBookPayload));

        ajaxClient.uploadFile("/library-management/add-new-book", formData, function(data){
            $("#book-status-modal-container").html(data);
            var updateForm = $("#success").text().trim();
            if(updateForm == "True"){
                manageBook.loadUpdateBookDetailsHome();
                $("#updateBookNav").parent().addClass("active");
                $("#addBookNav").parent().removeClass("active");
            }else{
                $("#book-status-modal").modal('toggle');
            }
        });
    },
    returnIndividualBookDetailsListWithOutAccess : function(accessionId, bookId, accessionNumber){
        var rack = $("#book-rack").val().trim();
        if(rack == ""){
            rack = null;
        } 
        var price = $("#book-price").val().trim();
        if(price == ""){
            price = null;
        }
        var mrp = $("#book-mrp").val().trim();
        if(mrp == ""){
            mrp = null;
        }
        var billNumber = $("#book-bill-number").val().trim();
        if(billNumber == ""){
            billNumber = null;
        }
        var purchaseDate = null;
        var checkPurchaseDate = getDate($(".book-purchase-date").val());
        if( checkPurchaseDate != null){
        purchaseDate =  checkPurchaseDate.getTime() / 1000;
        }
        var vendorId = $("#book-vendor").val().trim();
        if(vendorId == ""){
            vendorId = null;
        }
        var libraryTypeId = $("#book-library-type").val().trim();
        if(libraryTypeId == ""){
            libraryTypeId = null;
        }
        var bookVolume= $("#book-volume").val().trim();
        if(bookVolume == ""){
            bookVolume = null;
        }
        var addedOnDate =  null;
        var checkAddedOnDate = getDate($(".book-added-on-date").val());
        if(checkAddedOnDate != null){
            addedOnDate = checkAddedOnDate.getTime() / 1000;
        }
        var status = $("#book-status").val();
        if(status == ""){
            showErrorDialogBox("Invalid Status of book ");
            return false;
        }
        var remarks = $("#book-remarks").val();
        if(remarks == ""){
            remarks = null;
        }

        var individualBookDetail = {"accessionId": accessionId, "bookId": bookId, "accessionNumber": accessionNumber, "rack": rack, "purchasePrice": price, "mrp": mrp, "billNumber": billNumber, "dateOfPurchase": purchaseDate, "vendorId": vendorId, "status": status, "volume": bookVolume, "addedOn" : addedOnDate, "libraryTypeId": libraryTypeId, "remarks": remarks};
        
        return individualBookDetail;

    },
    returnIndividualBookDetailsListWithAccess : function(bookId){
        individualBookDetailList = [];
        $(".book-details-row").each(function() {
            var invalid = validateMandatoryFields($(this));
            if(invalid){
                showErrorDialogBox("Invalid book information. Please try again.")
                return false;
            }
            var accessionId = $(this).find(".accessionId").text().trim();
            if(accessionId == ""){
                accessionId = null;
            }
            var accessionNumber = $(this).find(".accession-number").val().trim();
            if(accessionNumber == ""){
                showErrorDialogBox("Invalid Accession Number Can't be Empty");
                return false;
            }

            // Get values from input fields within the current row
            var rack = $(this).find(".book-rack").val().trim();
            if(rack == ""){
                rack = null;
            } 
            var price = $(this).find(".book-price").val().trim();
            if(price == ""){
                price = null;
            }
            else{
                price = parseFloat(price);
            }
            var mrp = $(this).find(".book-mrp").val().trim();
            if(mrp == ""){
                mrp = null;
            }
            else{
                mrp = parseFloat(mrp);
            }
            var billNumber = $(this).find(".book-bill-number").val().trim();
            if(billNumber == ""){
                billNumber = null;
            }
            var purchaseDate =  null;
            var checkPurchaseDate = getDate($(this).find(".book-purchase-date").val());
            if(checkPurchaseDate != null){
                purchaseDate = checkPurchaseDate.getTime() / 1000;
            }
            var vendorId = $(this).find(".book-vendor").val().trim();
            if(vendorId == ""){
                vendorId = null;
            }
            var libraryTypeId = $(this).find(".book-library-type").val().trim();
            if(libraryTypeId == ""){
                libraryTypeId = null;
            }
            var bookVolume= $(this).find(".book-volume").val().trim();
            if(bookVolume == ""){
                bookVolume = null;
            }
            var addedOnDate =  null;
            var checkAddedOnDate = getDate($(this).find(".book-added-on-date").val());
            if(checkAddedOnDate != null){
                addedOnDate = checkAddedOnDate.getTime() / 1000;
            }
            var status = $(this).find(".book-status").val();
            if(status == ""){
                showErrorDialogBox("Invalid Status of book ");
                return false;
            }
            var remarks = $(this).find(".book-remarks").val();
            if(remarks == ""){
                remarks = null;
            }

            var individualBookDetail = {"accessionId": accessionId, "bookId": bookId, "accessionNumber": accessionNumber, "rack": rack, "purchasePrice": price, "mrp": mrp, "billNumber": billNumber, "dateOfPurchase": purchaseDate, "vendorId": vendorId, "status": status, "volume": bookVolume, "addedOn": addedOnDate, "libraryTypeId": libraryTypeId, "remarks": remarks};
            
            individualBookDetailList.push(individualBookDetail);
        });
        return individualBookDetailList;
    },
    back: function(){
        manageBook.loadUpdateBookDetailsHome("");
    },
    loadUpdateBookDetailsHome : function (){
        ajaxClient.get("/library-management/update-book-View", function(data){
            $("#main-content").html(data);
            initDate(365*100);
            if ($('#searchbooks').length > 0) {
                manageBook.searchBook(true);
                manageBook.registerBookSearchCallback();
            } else {
                console.error("Search button does not exist after content load.");
            }
        })
    },
    registerBookSearchCallback : function(){
        $('#searchbooks').on('click', function () {
            manageBook.searchBook(true);
       });
       $("#searchBookInput").on('keyup', function (e) {
         if (e.keyCode == 13) {
            manageBook.searchBook(true);
         }
       });
    },
    searchBook: function(freshSearch){
        var search_text = $("#searchBookInput").val();
        if(search_text == undefined){
            search_text = "";
        }
        var page_number = $('.page-item.active').find('.page-number').text().trim();
        if(freshSearch){
            page_number = 1;
        }
        var itemsPerPage = $('#items-per-page').val();
        if(page_number == null || page_number == ''){
          page_number = 1;
        }
        var offset = (page_number - 1)*itemsPerPage;
        $("#searchBookResult").html("");
        ajaxClient.get("/library-management/book-details"+"/"+offset+"/"+itemsPerPage+"?text="+search_text, function(data) {
              $("#searchBookResult").html(data);
                var paginationText = $("#pagination-info").text().trim();
                if (paginationText) {
                    try {
                        var response = JSON.parse(paginationText);
                        $('#items-per-page').val(response.itemsPerPage);
                        $('.page-item').removeClass('active');
                        var pageNumber = (response.offset / response.itemsPerPage) + 1;
                        $('#page-number-' + pageNumber).addClass('active');
                    } catch (e) {
                        console.error("Error parsing JSON: ", e);
                    }
                } else {
                    console.error("Pagination info is empty.");
                }
                manageBook.registerDeleteBookDeatilsCallBack();
                manageBook.registerUpdateBookDetailsCallBack();
                manageBook.registerUpdateNumberOfBookCallBack();
                manageBook.registerViewBookDetailsCallBack();
                manageBook.registerUploadBookDocumentCallBack();
                bookList.initPagination(); 
          });
    },
    registerViewBookDetailsCallBack : function(){
        $('.view-book-info').on('click', function () {
            var bookInfoJson = $(this).parent().parent().find('.book-info-json').text().trim();
            var bookInfo = JSON.parse(bookInfoJson);
            var access = $(".library-accession-number-access").text().trim();
            $('#book-id').text(bookInfo.bookId);
            $('#book-page-text').text("View Book");
            $('#update-book-button').css('display', 'none');
            $('#book-title').val(bookInfo.bookTitle);
            $('#book-title').prop('disabled', true);
            $('#book-number').val(bookInfo.bookNo == null ? "" : bookInfo.bookNo);
            $('#book-number').prop('disabled', true);
            $('#book-genre').val(bookInfo.genre == null ? "" : bookInfo.genre.genreId);
            $('#book-genre').prop('disabled', true);
            $('#book-author').val(bookInfo.author == null ? "" : bookInfo.author.authorId);
            $('#book-author').prop('disabled', true);
            $('#book-isbn-number').val(bookInfo.isbn);
            $('#book-isbn-number').prop('disabled', true);
            $('#book-publication').val(bookInfo.publication == null ? "": bookInfo.publication.publicationId);
            $('#book-publication').prop('disabled', true);
            $('#book-publisher').val(bookInfo.publisher == null ? "" : bookInfo.publisher.publisherId);
            $('#book-publisher').prop('disabled', true);
            $('#book-edition').val(bookInfo.edition == null ? "" : bookInfo.edition);
            $('#book-edition').prop('disabled', true);
            $('#book-copies').val(bookInfo.noOfCopies);
            $('#book-copies').prop('disabled', true);
            $('#book-publication-year').val(bookInfo.publishYear == null ? "" : bookInfo.edition);
            $('#book-publication-year').prop('disabled', true);
            $('#book-language').val(bookInfo.language == null ? "" : bookInfo.language);
            $('#book-language').prop('disabled', true);
            $('#book-binding').val(bookInfo.typeOfBinding == null ? "" : bookInfo.typeOfBinding);
            $('#book-binding').prop('disabled', true);
            $('#book-pages').val(bookInfo.numberOfPages == 0 ? "" : bookInfo.numberOfPages);
            $('#book-pages').prop('disabled', true);
            $('#book-tags').val(bookInfo.tags == null ? "" : bookInfo.tags);
            $('#book-tags').prop('disabled', true);
            if(bookInfo.coverImage != null){
                  $('#update-book-photo-label').html("Upload Photo <span style='color:green;'> (Image already uploaded) </span>");
              }else{
                  $('#update-book-photo-label').html("Upload Photo <span style='color:red;'> (Image not uploaded yet) </span>");
              }
            if(access == "true"){
                manageBook.populateSingleBookTable(".individualBookDetails", bookInfo.individualBookDetails, bookInfo.noOfCopies, true);
                $('.row-checkbox-div').css('display' ,'none');
                $('.row-checkbox').css('display', 'none');
                $('.select-all-checkbox').css('display' , 'none');
            }
            else{
                individualBookDetail = bookInfo.individualBookDetails[0];
                if(individualBookDetail.dateOfPurchase != null){
                const formattedDate = getFormattedDate(individualBookDetail.dateOfPurchase);

                // Set the value of the input field
                $(".book-purchase-date").val(formattedDate);
                }

                $("#book-bill-number").val(individualBookDetail.billNumber == null ? "" : individualBookDetail.billNumber);
                $('#book-bill-number').prop('disabled', true);
                $('.book-purchase-date').prop('disabled', true);
                $("#book-vendor").val(individualBookDetail.vendor == null ? "" : individualBookDetail.vendor.vendorId);
                $('#book-vendor').prop('disabled', true);
                $("#book-price").val(individualBookDetail.purchagePrice == 0.0 ? "" : individualBookDetail.purchagePrice);
                $('#book-price').prop('disabled', true);
                $("#book-rack").val(individualBookDetail.rack == null ? "" : individualBookDetail.rack);
                $('#book-rack').prop('disabled', true);
                $("#book-library-type").val(individualBookDetail.libraryType == null ? "" : individualBookDetail.libraryType.libraryTypeId);
                $('#book-library-type').prop('disabled', true);
                $("#book-volume").val(individualBookDetail.volume == null ? "" : individualBookDetail.volume);
                $('#book-volume').prop('disabled', true);
                $("#book-status").val(individualBookDetail.status);
                $('#book-status').prop('disabled', true);
                $("#book-remark").val(individualBookDetail.remarks == null ? "" : individualBookDetail.remarks);
                $('#book-remark').prop('disabled', true);
            }
            $('.book-details-screen').attr('style','display:none');
            $('#book-list\\.update-book-screen').attr('style','display:block');
          });
    },
    registerDeleteBookDeatilsCallBack : function(){
        $('.delete-book-info').on('click', function () {
            var bookInfoJson = $(this).parent().parent().find('.book-info-json').text().trim();
            var bookDetail = JSON.parse(bookInfoJson);
            var bookId = bookDetail.bookId;
            $("#book-delete-confirm-button").attr("onclick","manageBook.deleteBookDetails('"+bookId+"')");
            $("#book-delete-warning-popup").modal('toggle');
       });
    },




    
    registerUpdateNumberOfBookCallBack : function(){
        $('.add-number-of-copies').on('click', function(){
            $('#popup-modal-type-for-book-details').text("Add Number Of Copies");
            $('.update-book-detail-button').text("Add Copies");
            var bookInfoJson = $(this).parent().parent().parent().find('.book-info-json').text().trim();
            var bookInfo = JSON.parse(bookInfoJson);
            var bookId = bookInfo.bookId;
            var access = $(".library-accession-number-access").text().trim();
            if(access == "true"){
                $('#update-book-copies').on('change', function () {
                    var numberOfCopies = $('#update-book-copies').val();
                    if(access && numberOfCopies > 0){
                        $('#update-copies-error').text("");
                        manageBook.populateSingleBookTable(".update-book-detail-popup", {}, numberOfCopies, false);
                        $('.row-checkbox-div').css('display' ,'none');
                        $('.row-checkbox').css('display' ,'none');
                        $('.select-all-checkbox').css('display','none');
                    }
                    else{
                        $('#update-copies-error').text("Number of copies Can't be zero");
                        $('.update-book-detail-popup').html("");
                    }
                    });
            }
            else{
                individualBookDetail = bookInfo.individualBookDetails[0];
                if(individualBookDetail.dateOfPurchase != null){
                const formattedDate = getFormattedDate(individualBookDetail.dateOfPurchase);

                // Set the value of the input field
                $(".update-book-purchase-date").val(formattedDate);
                }
                $("#update-book-bill-number").val(individualBookDetail.billNumber == null ? "" : individualBookDetail.billNumber);
                $("#update-book-vendor").val(individualBookDetail.vendor == null ? "" : individualBookDetail.vendor.vendorId);
                $("#update-book-price").val(individualBookDetail.purchagePrice == 0.0 ? "" : individualBookDetail.purchagePrice);
                $("#update-book-rack").val(individualBookDetail.rack == null ? "" : individualBookDetail.rack);
                $("#update-book-library-type").val(individualBookDetail.libraryType == null ? "" : individualBookDetail.libraryType.libraryTypeId);
                $("#update-book-volume").val(individualBookDetail.volume == null ? "" : individualBookDetail.volume);
                $("#update-book-status").val(individualBookDetail.status);
                $("#update-book-remark").val(individualBookDetail.remarks == null ? "" : individualBookDetail.remarks);
            }
          $(".update-book-detail-button").attr("onclick","manageBook.updateNumberOfBookDetails('"+bookId+"')");
          $('#update-book-details-modal').modal('toggle');
        });
        $('.delete-number-of-copies').on('click', function(){
            $('#add-number-of-book-access-number').css('display', 'none');
            $('#popup-modal-type-for-book-details').text("Delete Number Of Copies");
            $('.update-book-detail-button').text("Delete Copies");
            var bookInfoJson = $(this).parent().parent().parent().find('.book-info-json').text().trim();
            var bookInfo = JSON.parse(bookInfoJson);
            var bookId = bookInfo.bookId;
            var access = $(".library-accession-number-access").text().trim();
            if(access == "true"){
                $('.update-book-copies-div').css('display', 'none');
                var bookInfoJson = $(this).parent().parent().parent().find('.book-info-json').text().trim();
                var bookInfo = JSON.parse(bookInfoJson);
                manageBook.populateSingleBookTable(".update-book-detail-popup", bookInfo.individualBookDetails, bookInfo.noOfCopies, false);
            }
            $(".update-book-detail-button").attr("onclick","manageBook.updateNumberOfBookDetails('"+bookId+"')");
            $('#update-book-details-modal').modal('toggle');
        });
        $('#update-book-details-modal').on('hidden.bs.modal', function () {
            $('#add-number-of-book-access-number').css('display', '');
            $('.update-book-copies-div').css('display', '');
        });
    },
    updateNumberOfBookDetails: function (bookId) {
        var popupText = $('#popup-modal-type-for-book-details').text().trim();
        $('#update-copies-error').text("");
        var access = $(".library-accession-number-access").text().trim();
        var updateCopiesPayload = { bookId: bookId, dataUpdationAction: "" };
    
        if (popupText === "Add Number Of Copies") {
            var numberOfCopies = $('#update-book-copies').val();
            if (numberOfCopies <= 0) {
                $('#update-copies-error').text("Number of copies can't be zero");
                return false;
            }
    
            var individualBookDetailList = access === "true" ? manageBook.returnIndividualBookDetailsListWithAccess(bookId) : [];
            if (access === "true" && individualBookDetailList === false) return;
    
            if (access !== "true") {
                var getValue = (selector, defaultValue = null) => {
                    var val = $(selector).val().trim();
                    return val === "" ? defaultValue : val;
                };

                var dateTimestamp = getDate($(".update-book-purchase-date").val());
                individualBookDetailList.push({
                    accessionId: null,
                    bookId: bookId,
                    accessionNumber: null,
                    rack: getValue("#update-book-rack"),
                    price: getValue("#update-book-price"),
                    billNumber: getValue("#update-book-bill-number"),
                    dateOfPurchase: dateTimestamp == null ? null : dateTimestamp.getTime() / 1000,
                    vendorId: getValue("#update-book-vendor"),
                    status: getValue("#update-book-status"),
                    volume: getValue("#update-book-volume"),
                    libraryTypeId: getValue("#update-book-library-type"),
                    remarks: getValue("#update-book-remarks")
                });
    
                if (!individualBookDetailList[0].status) {
                    showErrorDialogBox("Invalid Status of book");
                    return false;
                }
            }
    
            updateCopiesPayload.numberOfCopies = numberOfCopies;
            updateCopiesPayload.individualBookDetailsPayloads = individualBookDetailList;
            updateCopiesPayload.dataUpdationAction = "ADD";
        } 
        else { // Deletion Case
            var accessionIdList = [];
            if (access === "true") {
                $(".book-details-row").each(function () {
                    if ($(this).find(".row-checkbox").prop("checked")) {
                        accessionIdList.push($(this).find(".accessionId").text().trim());
                    }
                });
    
                if (accessionIdList.length === 0) {
                    showErrorDialogBox("At least select one of the checkboxes.");
                    return false;
                }
    
                updateCopiesPayload.numberOfCopies = accessionIdList.length;
                updateCopiesPayload.accessionIdList = accessionIdList;
            } else {
                updateCopiesPayload.numberOfCopies = numberOfCopies;
            }
            updateCopiesPayload.dataUpdationAction = "DELETE";
        }
    
        ajaxClient.post("/library-management/update-number-of-book", JSON.stringify(updateCopiesPayload), function (data) {
            $("#main-book-status-modal-container").html(data);
            $('#update-book-details-modal').modal('hide');
            var response = $("#success").text().trim();
    
            if (response === "True") {
                manageBook.loadUpdateBookDetailsHome();
            } else {
                $("#book-update-status-modal").modal({ backdrop: 'static', keyboard: false }).modal('toggle');
            }
        });
    },    




    registerUpdateBookDetailsCallBack : function(){
        $('.update-book-info').on('click', function () {
            var bookInfoJson = $(this).parent().parent().parent().find('.book-info-json').text().trim();
            var bookInfo = JSON.parse(bookInfoJson);
            var access = $(".library-accession-number-access").text().trim();
            $('#book-id').text(bookInfo.bookId);
            $('#book-title').val(bookInfo.bookTitle);
            $('#book-number').val(bookInfo.bookNo == null ? "" : bookInfo.bookNo);
            $('#book-genre').val(bookInfo.genre == null ? "" : bookInfo.genre.genreId);
            $('#book-author').val(bookInfo.author == null ? "" : bookInfo.author.authorId);
            $('#book-isbn-number').val(bookInfo.isbn);
            $('#book-publication').val(bookInfo.publication == null ? "": bookInfo.publication.publicationId);
            $('#book-publisher').val(bookInfo.publisher == null ? "" : bookInfo.publisher.publisherId);
            $('#book-edition').val(bookInfo.edition == null ? "" : bookInfo.edition);
            $('#book-copies').val(bookInfo.noOfCopies);
            $('#book-copies').prop('disabled', true);
            $('#book-publication-year').val(bookInfo.publishYear == null ? "" : bookInfo.edition);
            $('#book-language').val(bookInfo.language == null ? "" : bookInfo.language);
            $('#book-binding').val(bookInfo.typeOfBinding == null ? "" : bookInfo.typeOfBinding);
            $('#book-pages').val(bookInfo.numberOfPages == 0 ? "" : bookInfo.numberOfPages);
            $('#book-tags').val(bookInfo.tags == null ? "" : bookInfo.tags);

            if(bookInfo.coverImage != null){
                  $('#update-book-photo-label').html("Upload Photo <span style='color:green;'> (Image already uploaded) </span>");
              }else{
                  $('#update-book-photo-label').html("Upload Photo <span style='color:red;'> (Image not uploaded yet) </span>");
              }
            if(access == "true"){
                manageBook.populateSingleBookTable(".individualBookDetails", bookInfo.individualBookDetails, bookInfo.noOfCopies, false);
                $('.row-checkbox-div').css('display' ,'none');
                $('.row-checkbox').css('display' , 'none');
                $('.select-all-checkbox').css('display' , 'none');
            }
            else{
                individualBookDetail = bookInfo.individualBookDetails[0];
                if(individualBookDetail.dateOfPurchase != null){
                const formattedDate = getFormattedDate(individualBookDetail.dateOfPurchase);

                // Set the value of the input field
                $(".book-purchase-date").val(formattedDate);
                }
                $("#book-bill-number").val(individualBookDetail.billNumber == null ? "" : individualBookDetail.billNumber);
                $("#book-vendor").val(individualBookDetail.vendor == null ? "" : individualBookDetail.vendor.vendorId);
                $("#book-price").val(individualBookDetail.purchagePrice == 0.0 ? "" : individualBookDetail.purchagePrice);
                $("#book-rack").val(individualBookDetail.rack == null ? "" : individualBookDetail.rack);
                $("#book-library-type").val(individualBookDetail.libraryType == null ? "" : individualBookDetail.libraryType.libraryTypeId);
                $("#book-volume").val(individualBookDetail.volume == null ? "" : individualBookDetail.volume);
                $("#book-status").val(individualBookDetail.status);
                $("#book-remark").val(individualBookDetail.remarks == null ? "" : individualBookDetail.remarks);
            }
            $('.book-details-screen').attr('style','display:none');
          $('#book-list\\.update-book-screen').attr('style','display:block');
        });
    },
    registerUploadBookDocumentCallBack : function(){
        $('.upload-book-document').on('click', function () {
          var bookInfoJson = $(this).parent().parent().find('.book-info-json').text().trim();
          var bookInfo = JSON.parse(bookInfoJson);
          $("#upload-document-book-id").text(bookInfo.bookId);
          $('.book-details-screen').attr('style','display:none');
          $('#book-list\\.upload-document-screen').attr('style','display:block');
          $("#upload-document-book-title").text(bookInfo.bookTitle);
          manageBook.populateUploadedDocuments(bookInfo.bookDocument);
        });
    },
    returnToMainScreen : function(){
        manageBook.searchBook(true);
        $('.book-details-screen').attr('style','display:none');
        $('#book-details').attr('style','display:block');
    },
    deleteBookDetails : function(bookId){
        ajaxClient.post("/library-management/delete-book/"+bookId,{},function(data){
            $("#main-book-status-modal-container").html(data);
            $("#book-delete-warning-popup").modal('hide');
            var response = $("#success").text().trim();
    
            if(response == "True"){
                manageBook.loadUpdateBookDetailsHome(); 
            }else{
                $("#book-update-status-modal").modal('toggle');
            }
        })
    },
    updateBookDetails : function(){
        var invalid = validateMandatoryFields($("#update\\.book-info-content"));
        if(invalid){
          showErrorDialogBox("Invalid book information. Please try again.")
          return;
        }
        var updatedIndividualBookDetailList = [];
        var access = $(".library-accession-number-access").text().trim();
        var bookId = $("#book-id").text().trim();
        var noOfCopies = $("#book-copies").val().trim();
        if(access == "true"){
            var returnData = manageBook.returnIndividualBookDetailsListWithAccess(bookId);
            if(returnData == false){
                return;
            }
            updatedIndividualBookDetailList = returnData;
        }
        else{
            var bookInfoJson = $('#book-details-json').text().trim();
            var bookInfo = JSON.parse(bookInfoJson);

            var individualBookDetailList = bookInfo[0].individualBookDetails;

            for(var j =0; j<noOfCopies; j++){
                var individualBookDetail = individualBookDetailList[j];
                var returnData = manageBook.returnIndividualBookDetailsListWithOutAccess(individualBookDetail.accessionId, bookId, individualBookDetail.accessionNumber);
                if(returnData == false){
                    return;
                }
                updatedIndividualBookDetailList.push(returnData);
            }
        }
       
        var bookTitle = $("#book-title").val().trim();
        var bookNo = $("#book-number").val().trim();
        var genreId = $("#book-genre").val().trim();
        var authorId = $("#book-author").val().trim();
        var isbnNumber = $("#book-isbn-number").val().trim();
        var publicationId = $("#book-publication").val().trim();
        var publisherId = $("#book-publisher").val().trim();
        var edition = $("#book-edition").val().trim();
        var publicationYear = $("#book-publication-year").val().trim();
        var language = $("#book-language").val().trim();
        var numberOfPages = $("#book-pages").val().trim(); 
        var typeOfBinding = $("#book-binding").val().trim();
       
        var tags = $("#book-tags").val().trim();
        var tagsSet = [];

        if(noOfCopies<=0){
            $("#copies-error").text("Need greater than Zero value");
            return;
        }

        if(bookNo == ""){
            bookNo = null;
        }
        if(genreId == ""){
            genreId = null;
        }
        if(authorId == ""){
            authorId = null;
        }
        if(publicationId == ""){
            publicationId = null;
        }
        if(publisherId == ""){
            publisherId = null;
        }
        if(edition == ""){
            edition = null;
        }
        if(publicationYear == ""){
            publicationYear = null;
        }
        if(language == ""){
            language = null;
        }
        if(typeOfBinding == ""){
            typeOfBinding = null;
        }
        if(numberOfPages == ""){
            numberOfPages = null;
        }
        if(tags == ""){
            tags = null;
        }
        var bookDetails = {'bookId': bookId, 'genreId': genreId, 'bookTitle': bookTitle, 'bookNo': bookNo, 'authorId' : authorId, 'isbn' : isbnNumber, 'publicationId' : publicationId, 'publisherId': publisherId, 'edition' : edition, 'noOfCopies' : noOfCopies, 'publishYear': publicationYear, 'language' : language, 'typeOfBinding': typeOfBinding, 'numberOfPages' : numberOfPages}
        if (tags && tags.includes(',')) {
            var tagsArray = tags.split(',').map(tag => tag.trim());
            tagsSet = new Set(tagsArray);
        }else if(tags){
            tagsSet = new Set([tags.trim()]);
        }
      
        var updateBookPayload = {'bookDetailPayload': bookDetails, 'tags': Array.from(tagsSet), 'individualBookDetailsPayloads': updatedIndividualBookDetailList}
        ajaxClient.post("/library-management/update-book",  JSON.stringify(updateBookPayload), function(data){
            $("#book-status-modal-container").html(data);
            $("#book-document-warning-popup").modal('hide');
            var response = $("#success").text().trim();
            if(response == "True"){
                $('#book-list\\.update-book-screen').attr('style','display:none');
                $('.book-details-screen').attr('style','display:block');
                manageBook.loadUpdateBookDetailsHome();
            }else{
                $("#book-update-status-modal").modal('toggle');
                $("#book-update-status-modal").modal({ backdrop: 'static', keyboard: false });
            }
        });
    },
    resetNewDocumentUploadPopup :   function() {
       $("#upload-document-type").val("");
       $("#upload-document-file").val("");
       $("#upload-document-file-label").text("");
       $("#upload-document-name").val("");
       registerUploadFileCallback();
       $("#upload-new-document-modal").modal({
        backdrop: 'static',
        keyboard: false
        });
   },
 
    populateUploadedDocuments: function(bookDocuments) {
       if(bookDocuments === null || bookDocuments.length == 0) {
           $("#uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>");
            return;
       }
       var documentsList = "<br>";
       var itemsPerRow = 3;
       var numberOfDocument = 0;
       for(var i = 0 ; i < bookDocuments.length; i++){
           var bookDocument = bookDocuments[i];
           if(bookDocument.documentId == null || bookDocument.documentType == "BOOK_COVER_IMAGE_THUMBNAIL") {
             continue;
           }
           if(numberOfDocument % 3 == 0){
               if(numberOfDocument != 0){
                   documentsList += "</div>";
               }
               documentsList += "<div class=\"row\">";
           }
           numberOfDocument++;
           var uplaodTimeText = "Uploaded on : " + getFormattedDate(bookDocument.uploadTime);
           documentsList += "<div class=\"col-sm-4\"> <div class=\"card bg-light text-center\"> <div class=\"card-header\"> <h5> <strong> "+ bookDocument.documentName + " </strong></h5> </div> <div class=\"card-body\"> <p style=\"display:none;\" class=\"view-document-id\"> "+ bookDocument.documentId + " </p> <p class=\"card-text\"> Category : "+ bookDocument.documentTypeDisplayName+" </p> <a href=\"#\" class=\"btn btn-outline-info download-document\">Download </a> <a href=\"#\" class=\"btn btn-outline-danger delete-document\">Delete </a> </div> <div class=\"card-footer text-muted\"> "+ uplaodTimeText + " </div> </div> </div>"
         }
         documentsList += "</div> <br>";
         $("#uploaded-documents").html(documentsList);
         manageBook.bindDocumentActions();
     },
 
      bindDocumentActions : function() {
       $('.download-document').on('click', function () {
           var documentId = $(this).parent().find('p.view-document-id').text().trim();
           var bookId = $('#upload-document-book-id').text().trim();
           window.open(baseURL+"/library-management/document-download/"+documentId+"/"+bookId, '_blank');
       });
 
       $('.delete-document').on('click', function () {
           var documentId = $(this).parent().find('p.view-document-id').text().trim();
           var bookId = $('#upload-document-book-id').text().trim();
           $("#document-delete-confirm-button").attr("onclick","manageBook.deleteDocument('"+documentId+"','"+bookId+"')");
           $("#document-delete-confirm-modal").modal({backdrop: 'static', keyboard: false});
       });
     },
 
      uploadDocument: async function() {
         var documentType = $("#upload-document-type option:selected").val().trim();
         var bookId = $('#upload-document-book-id').text().trim();
         if(documentType == ""){
           showErrorDialogBox("Document type field is mandatory please fill it then proceed.");
           return;
         }
 
         var file ;
         var ONE_KB = 1024;
         var FILE_SIZE_LIMIT = 150;
         if (($("#upload-document-file"))[0].files.length > 0) {
             var uncompressedFile = ($("#upload-document-file"))[0].files[0];
             file = await compressFileUtils.compress(uncompressedFile);
             if((file.size / ONE_KB) > FILE_SIZE_LIMIT){
               showErrorDialogBox("Size Of document cannot be greater than " + FILE_SIZE_LIMIT + " kb");
               return;
             }
         } else {
             showErrorDialogBox("No file selected. Please choose a document to upload");
             return;
         }
         var documentName =  $("#upload-document-book-title").text();
         var formData = new FormData();
         formData.append('document', file);
         formData.append('documentType', documentType);
         formData.append('documentName', documentName);
         $("#upload-new-document-modal").modal('toggle');
         ajaxClient.uploadFile("/library-management/document-upload/"+bookId, formData, function(data){
             $("#manage-document-upload-status-modal-container").html(data);
             $("#book-document-upload-status-modal").modal({backdrop: 'static', keyboard: false});
             var bookDocumentsJson = $("#success-document-upload-response").text().trim();
             var bookDocuments = JSON.parse(bookDocumentsJson);
             manageBook.populateUploadedDocuments(bookDocuments);
         });
     },
     deleteDocument: function(documentId, bookId) {
           ajaxClient.post("/library-management/document-delete/"+documentId+"/"+bookId,{},function(data){
           $("#manage-document-upload-status-modal-container").html(data);
           $("#book-document-upload-status-modal").modal({backdrop: 'static', keyboard: false});
           var bookDocumentsJson = $("#success-document-upload-response").text().trim();
           var bookDocuments = JSON.parse(bookDocumentsJson);
           manageBook.populateUploadedDocuments(bookDocuments);
         });
     }
};
var issueBookDetailsMenu = {
    
    dataCache:{},

    loadMainScreen: function(){
        ajaxClient.get("/library-management/issued-book", function(data){
            $("#main-content").html(data);
            academicSessionHandler.bindSessionChangeEvent(issueBookDetailsMenu.changeSession);
            issueBookDetailsMenu.bindSearchStudentBookAssignmentEvent();
        });
    },

    doneStudentSearchTyping: function(resultArea) {
        var searchText = $('#book-assign-search-text').val().trim();
        var sessionId = academicSessionHandler.getSelectedSessionId();
        var status = "ENROLLED";
        studentLiveSearchEvent(sessionId, searchText, resultArea, issueBookDetailsMenu.loadStudentBookAssignment, status);
    },

    loadStudentBookAssignment: function(studentId) {
        var sessionId = academicSessionHandler.getSelectedSessionId();
        issueBookDetailsMenu.loadEntityBookAssignment(sessionId, STUDENT_ENTITY, studentId, STUDENT_ASSIGNED_BOOK_LIST, true, "ISSUED");
    },

    loadEntityBookAssignment: function(sessionId,entity,studentId,contentDiv,freshSearch, status) {
        entity = entity.trim();
        studentId = studentId.trim();
        if(sessionId == "" || entity == ""){
          showErrorDialogBox("Please select entity and session to assign fees.")
          return;
        }
        var page_number = $('.page-item.active').find('.page-number').text().trim();
        if(freshSearch){
            page_number = 1;
        }
        var itemsPerPage = $('#items-per-page').val();
        if(page_number == null || page_number == ''){
          page_number = 1;
        }
        var offset = (page_number - 1)*itemsPerPage;
        ajaxClient.get("/library-management/student-book-details/"+sessionId+"/"+entity+"/"+studentId+"?offset="+offset+"&limit="+itemsPerPage+"&status="+status, function(data) {
            $("#"+contentDiv).html(data);
            var paginationText = $("#pagination-info").text().trim();
                if (paginationText) {
                    try {
                        var response = JSON.parse(paginationText);
                        $('#items-per-page').val(response.itemsPerPage);
                        $('.page-item').removeClass('active');
                        var pageNumber = (response.offset / response.itemsPerPage) + 1;
                        $('#page-number-' + pageNumber).addClass('active');
                    } catch (e) {
                        console.error("Error parsing JSON: ", e);
                    }
                } else {
                    console.error("Pagination info is empty.");
                }
                if(status == "ISSUED"){
                    $('#nav-issued-book-tab').addClass('active');
                    $('#nav-returned-book-tab').removeClass('active');
                    
                    $('#issued-book-content').addClass('show active');
                    $('#return-book-content').removeClass('show active');
                }
                else if(status == "RETURNED"){
                    $('#nav-returned-book-tab').addClass('active');
                    $('#nav-issued-book-tab').removeClass('active');
        
                    $('#return-book-content').addClass('show active');
                    $('#issued-book-content').removeClass('show active');
                }
                else{
                    $('#nav-lost-book-tab').addClass('active');
                    $('#nav-issued-book-tab').removeClass('active');
                    $('#nav-returned-book-tab').removeClass('active');
        
                    $('#lost-book-content').addClass('show active');
                    $('#issued-book-content').removeClass('show active');
                    $('#return-book-content').removeClass('show active');
                }
                bookList.bookDetailsInitPagination(sessionId, entity, studentId, contentDiv, status);
                issueBookDetailsMenu.getLibraryBookTransactions(); 
                issueBookDetailsMenu.getLibraryNewBookIssueTransactions();
                issueBookDetailsMenu.getLibraryReturnBookTransactions();
                issueBookDetailsMenu.registerViewBookDetailsCallBack(status);
                issueBookDetailsMenu.registerDeleteBookDeatilsCallBack();
                issueBookDetailsMenu.registerUpdateBookDeatilsCallBack();
        });
    },
    changeSession: function() {
        var academicSession =  academicSessionHandler.getSelectedSession();
        $("#academic-year-display").text(academicSession.displayName);
    },
    registerViewBookDetailsCallBack: function(status) {
        $('.view-book-info').on('click', function () {
            // Retrieve JSON data from the `student-book-info` element in the row
            var bookInfoJson = $(this).closest('tr').find('.student-book-info').text().trim();
    
            // Parse the JSON data safely
            try {
                var bookInfo = JSON.parse(bookInfoJson);
    
                // Populate the modal with book details
                $('.basic-info-tags').css('display', 'none');
                $('#detail-view\\.book-title').text(bookInfo.bookDetails.bookTitle);
                $('#detail-view\\.book-genre').text(bookInfo.bookDetails.genre != null ? bookInfo.bookDetails.genre.genreName : "N/A");
                $('#detail-view\\.book-author').text(bookInfo.bookDetails.author != null ? bookInfo.bookDetails.author.authorName : "N/A");
                $('#detail-view\\.book-isbn-number').text(bookInfo.bookDetails.isbn || "N/A");
                $('#detail-view\\.book-publication').text(bookInfo.bookDetails.publication != null ? bookInfo.bookDetails.publication.publicationName : "N/A");
                $('#detail-view\\.book-edition').text(bookInfo.bookDetails.edition || "N/A");
                $('#detail-view\\.book-publisher').text(bookInfo.bookDetails.publisher != null ? bookInfo.bookDetails.publisher.publisherName : "N/A");
                $('#detail-view\\.book-publication-year').text(bookInfo.bookDetails.publishYear > 0 ? bookInfo.bookDetails.publishYear : "N/A");
                $('#detail-view\\.book-language').text(bookInfo.bookDetails.language || "N/A");
                $('#detail-view\\.book-rack').text(bookInfo.bookDetails.individualBookDetail.rack || "N/A");
                $('#detail-view\\.book-price').text(bookInfo.bookDetails.individualBookDetail.purchagePrice || "N/A");
                $('#detail-view\\.book-number').text(bookInfo.bookDetails.individualBookDetail.accessionNumber || "N/A");
                $('#detail-view\\.book-issue-date').text(getFormattedDate(bookInfo.issuedBookEntry.issuedAt) || "N/A");
                $('#detail-view\\.book-issued-by').text(bookInfo.issuedBookEntry.issuedBy.name || "N/A");
                $('#detail-view\\.book-due-date').text(getFormattedDate(bookInfo.issuedBookEntry.dueDate) || "N/A");
                $('#detail-view\\.book-return-date').text(getFormattedDate(bookInfo.issuedBookEntry.returnedAt) || "N/A");
                $('#detail-view\\.book-received-by').text(bookInfo.issuedBookEntry.receivedBy ? bookInfo.issuedBookEntry.receivedBy.name : "N/A");
                
                // Toggle the appropriate modal based on the status
                if (status === "ISSUED") {
                    $('#view-book-issued-details-modal').modal('toggle');
                } else if(status === "RETURNED") {
                    $('#view-book-returned-details-modal').modal('toggle');
                }else{
                    $('#view-book-lost-details-modal').modal('toggle');
                }
            } catch (e) {
                console.error("Error parsing JSON for book details:", e);
            }
        });
    },
    registerDeleteBookDeatilsCallBack : function(){
        $('.delete-book-info').on('click', function () {
            var academicSessionId = academicSessionHandler.getSelectedSessionId();
            var bookInfoJson = $(this).closest('tr').find('.student-book-info').text().trim();
            var bookInfo = JSON.parse(bookInfoJson);
            var transactionId = bookInfo.issuedBookEntry.transactionId;
            var status = bookInfo.issuedBookEntry.status;
            var studentId = $('#book-assign-view-student-id').text().trim();
            $("#book-delete-confirm-button").attr("onclick","issueBookDetailsMenu.deleteBookDetails('"+academicSessionId+"','"+transactionId+"','"+status+"','"+studentId+"')");
            $("#book-delete-warning-popup").modal('toggle');
       });
    },
    registerUpdateBookDeatilsCallBack : function(){
        $('.update-book-info').on('click', function (){
            var academicSessionId = academicSessionHandler.getSelectedSessionId();
            var bookInfoJson = $(this).closest('tr').find('.student-book-info').text().trim();
            var bookInfo = JSON.parse(bookInfoJson);
            var transactionId = bookInfo.issuedBookEntry.transactionId;
            var status = bookInfo.issuedBookEntry.status;
            var studentId = $('#book-assign-view-student-id').text().trim();
            $("#book-date-update-button").attr("onclick","issueBookDetailsMenu.updateBookDetails('"+academicSessionId+"','"+transactionId+"','"+status+"','"+studentId+"')");
            if(status == "ISSUED"){
                $('#book-date-update-popup-text').text("Issued Book Update Date");
                $('#book-date-update-text').text("Issued Date*");
            }
            else if(status == "RETURNED"){
                $('#book-date-update-popup-text').text("Returned Book Update Date");
                $('#book-date-update-text').text("Return Date*");
            }
            else{
                $('#book-date-update-popup-text').text("Lost Book Update Date");
                $('#book-date-update-text').text("Lost Date*");
            }
            $("#book-date-update-popup").modal('toggle');
            initDate(365*100);
            $(".book-date-update").val(getTodayDateInInputFormat());
        });
    },
    deleteBookDetails : function(academicSessionId, transactionId, status, studentId){
        ajaxClient.post("/library-management/delete-issued-Book-data/"+academicSessionId+"/"+transactionId,{},function(data){
            $("#library-book-status-modal-container").html(data);
            $("#book-delete-warning-popup").modal('hide');
            var Response = $("#success").text().trim();
            if(Response == "True"){
                issueBookDetailsMenu.loadEntityBookAssignment(academicSessionId,STUDENT_ENTITY,studentId,STUDENT_ASSIGNED_BOOK_LIST, true, status);
            }else{
            $("#issue-book-status-modal").modal('toggle');
            }
        })
    },
    updateBookDetails : function(academicSessionId, transactionId, status, studentId){
        var updateBookDate = 0;
            if($('div.bookDateUpdate').find('input.book-date-update').length > 0){
                updateBookDate =  getDate($('div.bookDateUpdate').find('input.book-date-update').val()).getTime() / 1000;
            }
            if(updateBookDate == 0){
                alert("Please select Book Date!");
                return;
            }
        $("#book-date-update-popup").modal('toggle');
        ajaxClient.post("/library-management/update-issued-book-date/"+academicSessionId+"/"+transactionId+"?status="+status+"&update_issue_book_date="+updateBookDate,{},function(data){
            $("#library-book-status-modal-container").html(data);
            var response = $("#success").text().trim();
            if(response == "True"){
                issueBookDetailsMenu.loadEntityBookAssignment(academicSessionId,STUDENT_ENTITY,studentId,STUDENT_ASSIGNED_BOOK_LIST, true, status);
            }else{
            $("#issue-book-status-modal").modal('toggle');
            }
        })
    },    
    bindSearchStudentBookAssignmentEvent: function() {
        var resultArea = "#book-assign-student-search-result";
    
        $('#book-assign-search-student').on('click', function () {
            issueBookDetailsMenu.doneStudentSearchTyping(resultArea);
        });
        $("#book-assign-search-text").on('keyup', function (e) {
        if (e.keyCode == 13) {
            issueBookDetailsMenu.doneStudentSearchTyping(resultArea);
        }
        });
        liveSearchHandler.bindEvent('#book-assign-search-text',resultArea,issueBookDetailsMenu.doneStudentSearchTyping);
    },
    getLibraryBookTransactions: function(){
        var studentId = $('#book-assign-view-student-id').text().trim();
        var sessionId = academicSessionHandler.getSelectedSessionId();
        $('#nav-issued-book-tab').on('click', function () {
            issueBookDetailsMenu.loadEntityBookAssignment(sessionId, STUDENT_ENTITY, studentId, STUDENT_ASSIGNED_BOOK_LIST, true, "ISSUED");
        });
        $('#nav-returned-book-tab').on('click', function () {
            issueBookDetailsMenu.loadEntityBookAssignment(sessionId, STUDENT_ENTITY, studentId, STUDENT_ASSIGNED_BOOK_LIST, true, "RETURNED");
        });
        $('#nav-lost-book-tab').on('click', function () {
            issueBookDetailsMenu.loadEntityBookAssignment(sessionId, STUDENT_ENTITY, studentId, STUDENT_ASSIGNED_BOOK_LIST, true, "LOST");
        });
    },
    getLibraryNewBookIssueTransactions : function(){
        var studentId = $('#book-assign-view-student-id').text().trim();
        var sessionId = academicSessionHandler.getSelectedSessionId();
        $('#book-issue-modal-student-id').text(studentId);
        $('#book-issue-modal-session-id').text(sessionId);
        $('#student-assign-new-book-button').on('click', function(){
            $("#book-issue-modal").modal('toggle');
            issueBookDetailsMenu.bindSearchBookEvent();
        });

    },
    getLibraryReturnBookTransactions: function (){
        $('#student-return-book-button').on('click', function(){
            var transactionIds = issueBookDetailsMenu.getSelectedBulkTransactionIds();
            var sessionId = academicSessionHandler.getSelectedSessionId();
            var studentId = $('#book-assign-view-student-id').text().trim();
            if(issueBookDetailsMenu.dataCache.selectedIssuedBookCount == undefined){
                issueBookDetailsMenu.dataCache.selectedIssuedBookCount = 0;
            }
            $('#book-Return-confirm-popup-text').text("Total Number Of Book Return : "+ issueBookDetailsMenu.dataCache.selectedIssuedBookCount);
            $('#book-return-conformation-popup').modal('toggle');
            $(".book-Return-date").val(getTodayDateInInputFormat());
            initDate(365*100);
            issueBookDetailsMenu.returnBookDetails(sessionId, studentId, transactionIds);
        });
    },
    bindSearchBookEvent : function () {
        var resultArea = "#books-search-result";
        $("#books-search").on('keyup', function (e) {
        if (e.keyCode == 13) {
            issueBookDetailsMenu.doneBookSearchTyping(resultArea);
        }
        });
        liveSearchHandler.bindEvent('#books-search', resultArea, issueBookDetailsMenu.doneBookSearchTyping);
    },

    doneBookSearchTyping: function(resultArea) {
        var searchText = $('#books-search').val().trim();
        issueBookDetailsMenu.bookLiveSearchEvent(searchText, resultArea);
    },

    bookLiveSearchEvent : function (searchText, resultArea){
        ajaxClient.get("/library-management/book-live-search?searchText=" + searchText , function(data) {
            $(resultArea).html(data);
            issueBookDetailsMenu.loadBookDetails();
        });
    },

    loadBookDetails : function () {
        $("#live-search-book-results tbody tr td").on("click", function() {
            var bookDetails = JSON.parse($(this).parent().find("td.book-info-td").text().trim());
            issueBookDetailsMenu.addSelectedBookRow(bookDetails, true, false, true);
        });
    },

    addSelectedBookRow : function (bookDetails, showCrossButton, showCheckboxButton, newElement) {
        var title = bookDetails.bookTitle;
        var isbn = bookDetails.isbn;
        var bookId = bookDetails.bookId;
        var individualBookDetail = bookDetails.individualBookDetailLites;
        var access = $('accession-number-access').text();
        var trRef = $(this).parent();
        var count = 0;
        var accessionOptions = individualBookDetail.map(function (detail) {
            if(count == 0){
                if(access == "true"){
                    count = 1;
                return "<option value=\"\">Select Accession Number</option>" ;
                }
            }
            return "<option value=\"" + detail.accessionId + "\">" + detail.accessionNumber + "</option>";
        }).join("");
        var closeButtonHTML = "";
        if(showCrossButton) {
        closeButtonHTML = " <button type=\"button\" class=\"close delete-book-row \" aria-label=\"Close\"> <span aria-hidden=\"true\">&times;</span> </button>";
        }
        var checkboxButtonHTML = "";
        if(showCheckboxButton) {
        checkboxButtonHTML = " <input type=\"checkbox\" class=\"delete-book-checkbox\" name=\"\" value=\"\"> &nbsp; &nbsp;";
        }
        var trHTML = "<tr class=\"selected_book_row " + (newElement ? "add" : "") + "\" id=\"" + bookId + "\">" +
        "<td class=\"book-title-td\" scope=\"row\">" + checkboxButtonHTML + title + " </td>" +
        "<td class=\"book-isbn-number-td\" scope=\"row\">" + isbn + " </td>" +
        "<td class=\"accession-number-td\" scope=\"row\">" +
        "<select class=\"form-control mandatory-field\" id=\"accession-number\">" +
        accessionOptions +
        "</select>" +
        "</td>" +
        "<td class=\"issue-book-duration-td\" scope=\"row\">" +
        "<input type=\"text\" class=\"form-control mandatory-field\" id=\"issue-book-duration\" placeholder=\"Enter issue book duration (e.g., 30)\">" +
        "</td>" +
        "<td class=\"book-issued-at-td\" scope=\"row\">" +
        "<input type=\"text\" class=\"form-control select-date book-issue-date mandatory-field\" placeholder=\"Issue Date\"/>" +
        "</td>" +
        "<td>" + closeButtonHTML + "</td>" +
        "</tr>";
        $("#book-search-row").before(trHTML);
        $(".book-issue-date").val(getTodayDateInInputFormat());
        initDate(365*100);
        $("#books-search-result").html("");
        $("#books-search").val("");
        issueBookDetailsMenu.deletePurchaseItemEntry();
    },

    deletePurchaseItemEntry : function () {
        $(".delete-book-row").click(function() {
            $(this).parent().parent().remove();
        });
    },
    
    issueBookDetails : function () {
        var issueBookDetailList = [];
        var academicSessionId = $('#book-issue-modal-session-id').text().trim();
        $(".selected_book_row.add").each(function() {
            var invalid = validateMandatoryFields($(this));
            if(invalid){
            showErrorDialogBox("Invalid book information. Please try again.")
            return false;
            }
            var bookId = $(this).attr("id");

            // Get values from input fields within the current row
            var accessionId = $(this).find("#accession-number").val().trim(); 
            var duration = $(this).find("#issue-book-duration").val().trim();
            var issuedAt =  getDate($(this).find(".book-issue-date").val()).getTime() / 1000;

            // Get values from outside the row (assuming these are not row-specific)
            var studentId = $('#book-issue-modal-student-id').text().trim();

            var userType = "STUDENT";

            var issueBookPayload = {"bookId": bookId, "accessionId": accessionId, "academicSessionId": academicSessionId, "issuedToUserId": studentId, "issuedToUserType": userType, "duration": duration, "issuedAt": issuedAt}
            
            issueBookDetailList.push(issueBookPayload);
        });

        if(issueBookDetailList.length <= 0) {
            alert("Please select atleast a book to issue!");
            return;
        }
        $("#book-issue-modal").modal('toggle');
        ajaxClient.post("/library-management/issue-book/"+academicSessionId, JSON.stringify(issueBookDetailList), function(data) {
            $("#issue-book-transaction-detail-modal-container").html(data);
            var Response = $("#success").text().trim();
            if(Response == "True"){
                var bookDetails = issueBookDetailList[0]
                issueBookDetailsMenu.loadEntityBookAssignment(bookDetails.academicSessionId,STUDENT_ENTITY,bookDetails.issuedToUserId,STUDENT_ASSIGNED_BOOK_LIST, true, "ISSUED");
            }else{
                $("#issue-book-status-modal").modal('toggle');
            }
        });
    },

    returnBookDetails : function(academicSessionId, studentId, transactionId){
        $("#book-return-confirm-button").on("click", function() {
            if(issueBookDetailsMenu.dataCache.selectedIssuedBookCount == 0){
                $('#book-return-conformation-popup').modal('hide');
                alert("Please select book first to return");
                return;
            }
            var returnDate = 0;
            if($('div.returnDate').find('input.book-Return-date').length > 0){
                returnDate =  getDate($('div.returnDate').find('input.book-Return-date').val()).getTime() / 1000;
            }
            if(returnDate == 0){
                alert("Please select Return Book Date!");
                return;
            }
            var returnBookPayload = {"academicSessionId": academicSessionId, "transactionIds": transactionId, "returnDate": returnDate, "issueStatus": "RETURNED"}
            $('#book-return-conformation-popup').modal('toggle');
            ajaxClient.post("/library-management/return-books", JSON.stringify(returnBookPayload),function(data) {
                $("#issue-book-transaction-detail-modal-container").html(data);
                var response = $("#success").text().trim();
                if(response == "True"){
                    issueBookDetailsMenu.loadEntityBookAssignment(academicSessionId,STUDENT_ENTITY,studentId,STUDENT_ASSIGNED_BOOK_LIST, true, "RETURNED");
                }else{
                    $("#issue-book-status-modal").modal('toggle');
                } 
            });
            
        });
    
    },
    selectStudentIssuedBookList : function (selectAllCheckbox) {
        if(selectAllCheckbox.checked){
            $(".student-issued-books-select-checkbox").prop('checked', true);
            var selectedStudentIssuedBookCount = $('input.student-issued-books-select-checkbox:checkbox:checked').length;
            $("#student-issued-books-selected-count").html(selectedStudentIssuedBookCount);
            issueBookDetailsMenu.dataCache.selectedIssuedBookCount = selectedStudentIssuedBookCount;
        }
        else{
            $(".student-issued-books-select-checkbox").prop('checked', false);
            $("#student-issued-books-selected-count").html(0);
            issueBookDetailsMenu.dataCache.selectedIssuedBookCount = 0;
        }
    },
    studentIssuedBookSelectCheckbox : function(studentIssuedBookSelectCheckbox) {
        var selectedStudentIssuedBookCount = $('input.student-issued-books-select-checkbox:checkbox:checked').length;
        $("#student-issued-books-selected-count").html(selectedStudentIssuedBookCount);
        issueBookDetailsMenu.dataCache.selectedIssuedBookCount = selectedStudentIssuedBookCount;
    },
    getSelectedBulkTransactionIds :function () {
        var transactionIds = [];
        $("input.student-issued-books-select-checkbox").each(function() {
            if(!$(this).is(":checked")) {
                return;
            }
            var transactionId = $(this).parent().find('p.bulk-return-issued-transaction-id').first().text().trim();
            transactionIds.push(transactionId);
        });
        return transactionIds;
    },

};
var bookList = {
    dataCache : {},
  
    initPagination: function () {
      pagination.bindEvents(
        function() {
            manageBook.searchBook(false);
        },
        function () {
            manageBook.searchBook(false);
        },
        function () {
            manageBook.searchBook(false);
        },
        function () {
            manageBook.searchBook(true);
        }
      );
    },
    bookDetailsInitPagination: function (sessionId, entity, studentId, contentDiv, status) {
        pagination.bindEvents(
          function() {
            issueBookDetailsMenu.loadEntityBookAssignment(sessionId, entity, studentId, contentDiv, false, status);
          },
          function () {
            issueBookDetailsMenu.loadEntityBookAssignment(sessionId, entity, studentId, contentDiv, false, status);
          },
          function () {
            issueBookDetailsMenu.loadEntityBookAssignment(sessionId, entity, studentId, contentDiv, false, status);
          },
          function () {
            issueBookDetailsMenu.loadEntityBookAssignment(sessionId, entity, studentId, contentDiv, true, status);
          }
        );
      }
};
var libraryTypeDetails = {
    loadLibraryTypeDetailsHome : function (){
        ajaxClient.get("/library-management/library-type-view", function(data){
            $("#main-content").html(data);
            libraryTypeDetails.addLibraryTypeDetails();
            libraryTypeDetails.updateLibraryTypeDetails();
            libraryTypeDetails.deleteLibraryTypeDetails();
        })
    },
    addLibraryTypeDetails : function () {
        $('#add-library-type-button').on('click', function () {
            var libraryName = $("#library-type-name-textarea").val();
            if(libraryName === "" || libraryName === null || libraryName === undefined) {
                showErrorDialogBox("Please add library type name!");
                return;
            }
            
            var libraryTypePayload = {
                "libraryName": libraryName
            }
            
            ajaxClient.post("/library-management/add-library-type", {'libraryTypePayload' : JSON.stringify(libraryTypePayload)}, function(data){
                $("#library-type-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    $("#library-type-name-textarea").val("");
                    libraryTypeDetails.loadLibraryTypeDetailsHome();
                });
            });
        });
    },
    updateLibraryTypeDetails : function () {
        $('.update-library-type').on('click', function () {
            var libraryTypeInfoJson = $(this).parent().parent().find('.library-type-info-json').text().trim();
            var libraryTypeInfo = JSON.parse(libraryTypeInfoJson);
            $('#update-library-type-id').text(libraryTypeInfo.libraryTypeId);
            $('#library-type-name-text').val(libraryTypeInfo.libraryName);
            $("#update-library-type-popup-modal").modal('toggle');
        });
        $('#update-library-type-button').on('click', function () {
            var libraryTypeId = $("#update-library-type-id").text();
            var libraryName = $("#library-type-name-text").val();
            if(libraryName === "" || libraryName === null || libraryName === undefined) {
                showErrorDialogBox("Please add library type name!");
                return;
            }
            
            var libraryTypePayload = {
                "libraryTypeId": libraryTypeId,
                "libraryName": libraryName
            }
            
            $("#update-library-type-popup-modal").modal('toggle');
            ajaxClient.post("/library-management/update-library-type", {'libraryTypePayload' : JSON.stringify(libraryTypePayload)}, function(data){
                $("#library-type-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    libraryTypeDetails.loadLibraryTypeDetailsHome();
                });
            });
        });
    },
    deleteLibraryTypeDetails : function () {
        $('.delete-library-type').on('click', function () {
            var libraryTypeInfoJson = $(this).parent().parent().find('.library-type-info-json').text().trim();
            var libraryTypeInfo = JSON.parse(libraryTypeInfoJson);
            $('#delete-library-type-id').text(libraryTypeInfo.libraryTypeId);
            $("#delete-library-type-popup-modal").modal('toggle');
        });
        $('#delete-library-type-button').on('click', function () {
            var libraryTypeId = $("#delete-library-type-id").text();
            $("#delete-library-type-popup-modal").modal('toggle');
            
            ajaxClient.post("/library-management/delete-library-type/"+libraryTypeId, {}, function(data){
                $("#library-type-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    libraryTypeDetails.loadLibraryTypeDetailsHome();
                });
            });
        });
    },
};
var publicationDetails = {
    loadPublicationDetailsHome : function (){
        ajaxClient.get("/library-management/publication-view", function(data){
            $("#main-content").html(data);
            publicationDetails.addPublicationDetails();
            publicationDetails.viewPublicationDetails();
            publicationDetails.updatePublicationDetails();
            publicationDetails.deletePublicationDetails();
        })
    },
    addPublicationDetails : function () {
        $('#add-publication-button').on('click', function () {
            var publicationName = $("#publication-name").val();
            if(publicationName === "" || publicationName === null || publicationName === undefined) {
                showErrorDialogBox("Please add publication name!");
                return;
            }
            var address = $("#publication-address").val();
            var phone = $("#publication-phone").val();
            var email = $("#publication-email").val();
            var website = $("#publication-website").val();

            if(address === "" || address === null || address === undefined) {
                address = null;
            }
            if(phone === "" || phone === null || phone === undefined) {
                phone = null;
            }
            if(email === "" || email === null || email === undefined) {
                email = null;
            }
            if(website === "" || website === null || website === undefined) {
                website = null;
            }

            var publicationPayload = {"publicationName": publicationName, "address": address, "phoneNumber": phone, "email": email, "website": website}
            ajaxClient.post("/library-management/add-publication", {'publicationPayload' : JSON.stringify(publicationPayload)}, function(data){
                $("#publication-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    $("#publication-name").val("");
                    $("#publication-address").val("");
                    $("#publication-phone").val("");
                    $("#publication-email").val("");
                    $("#publication-website").val("");
                    publicationDetails.loadPublicationDetailsHome();
                });
            });
        });
    },
    viewPublicationDetails : function () {
        $('.view-publication').on('click', function () {
            var publicationInfoJson = $(this).parent().parent().find('.publication-info-json').text().trim();
            var publicationInfo = JSON.parse(publicationInfoJson);
            $('#view-publication-name').text(publicationInfo.publicationName);
            $('#view-publication-address').text(publicationInfo.address);
            $('#view-publication-phone').text(publicationInfo.phoneNumber);
            $('#view-publication-email').text(publicationInfo.email);
            $('#view-publication-website').text(publicationInfo.website);
            $("#view-publication-popup-modal").modal('toggle');
        });
    },
    updatePublicationDetails : function () {
        $('.update-publication').on('click', function () {
            var publicationInfoJson = $(this).parent().parent().find('.publication-info-json').text().trim();
            var publicationInfo = JSON.parse(publicationInfoJson);
            $('#update-publication-id').text(publicationInfo.publicationId);
            $('#publication-name-text').val(publicationInfo.publicationName);
            $('#publication-address-text').val(publicationInfo.address);
            $('#publication-phone-text').val(publicationInfo.phoneNumber);
            $('#publication-email-text').val(publicationInfo.email);
            $('#publication-website-text').val(publicationInfo.website);
            $("#update-publication-popup-modal").modal('toggle');
        });
        $('#update-publication-button').on('click', function () {
            var publicationId = $("#update-publication-id").text();
            var publicationName = $("#publication-name-text").val();
            if(publicationName === "" || publicationName === null || publicationName === undefined) {
                showErrorDialogBox("Please add publication name!");
                return;
            }
            var address = $("#publication-address-text").val();
            var phone = $("#publication-phone-text").val();
            var email = $("#publication-email-text").val();
            var website = $("#publication-website-text").val();

            if(address === "" || address === null || address === undefined) {
                address = null;
            }
            if(phone === "" || phone === null || phone === undefined) {
                phone = null;
            }
            if(email === "" || email === null || email === undefined) {
                email = null;
            }
            if(website === "" || website === null || website === undefined) {
                website = null;
            }

            var publicationPayload = {"publicationId": publicationId, "publicationName": publicationName, "address": address, "phoneNumber": phone, "email": email, "website": website}
            $("#update-publication-popup-modal").modal('toggle');
            
            ajaxClient.post("/library-management/update-publication", {'publicationPayload' : JSON.stringify(publicationPayload)}, function(data){
                $("#publication-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    publicationDetails.loadPublicationDetailsHome();
                });
            });
        });
    },
    deletePublicationDetails : function () {
        $('.delete-publication').on('click', function () {
            var publicationInfoJson = $(this).parent().parent().find('.publication-info-json').text().trim();
            var publicationInfo = JSON.parse(publicationInfoJson);
            $('#delete-publication-id').text(publicationInfo.publicationId);
            $("#delete-publication-popup-modal").modal('toggle');
        });
        $('#delete-publication-button').on('click', function () {
            var publicationId = $("#delete-publication-id").text();
            $("#delete-publication-popup-modal").modal('toggle');
            
            ajaxClient.post("/library-management/delete-publication/"+publicationId, {}, function(data){
                $("#publication-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    publicationDetails.loadPublicationDetailsHome();
                });
            });
        });
    },
};
var publisherDetails = {
    loadPublisherDetailsHome : function (){
        ajaxClient.get("/library-management/publisher-view", function(data){
            $("#main-content").html(data);
            publisherDetails.addPublisherDetails();
            publisherDetails.viewPublisherDetails();
            publisherDetails.updatePublisherDetails();
            publisherDetails.deletePublisherDetails();
        })
    },
    addPublisherDetails : function () {
        $('#add-publisher-button').on('click', function () {
            var publisherName = $("#publisher-name").val();
            if(publisherName === "" || publisherName === null || publisherName === undefined) {
                showErrorDialogBox("Please add publisher name!");
                return;
            }
            var contactInformation = $("#publisher-contact").val();
            var affiliation = $("#publisher-affiliation").val();
            var publisherPayload = {"publisherName": publisherName, "contactInformation": contactInformation, "affiliation": affiliation}
            ajaxClient.post("/library-management/add-publisher", {'publisherPayload' : JSON.stringify(publisherPayload)}, function(data){
                $("#publisher-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    $("#publisher-name").val("");
                    $("#publisher-contact").val("");
                    $("#publisher-affiliation").val("");
                    publisherDetails.loadPublisherDetailsHome();
                });
            });
        });
    },
    viewPublisherDetails : function () {
        $('.view-publisher').on('click', function () {
            var publisherInfoJson = $(this).parent().parent().find('.publisher-info-json').text().trim();
            var publisherInfo = JSON.parse(publisherInfoJson);
            $('#view-publisher-name').text(publisherInfo.publisherName);
            $('#view-publisher-contact').text(publisherInfo.contactInformation);
            $('#view-publisher-affiliation').text(publisherInfo.affiliation);
            $("#view-publisher-popup-modal").modal('toggle');
        });
    },
    updatePublisherDetails : function () {
        $('.update-publisher').on('click', function () {
            var publisherInfoJson = $(this).parent().parent().find('.publisher-info-json').text().trim();
            var publisherInfo = JSON.parse(publisherInfoJson);
            $('#update-publisher-id').text(publisherInfo.publisherId);
            $('#publisher-name-text').val(publisherInfo.publisherName);
            $('#publisher-contact-text').val(publisherInfo.contactInformation);
            $('#publisher-affiliation-text').val(publisherInfo.affiliation);
            $("#update-publisher-popup-modal").modal('toggle');
        });
        $('#update-publisher-button').on('click', function () {
            var publisherId = $("#update-publisher-id").text();
            var publisherName = $("#publisher-name-text").val();
            if(publisherName === "" || publisherName === null || publisherName === undefined) {
                showErrorDialogBox("Please add publisher name!");
                return;
            }
            var contactInformation = $("#publisher-contact-text").val();
            var affiliation = $("#publisher-affiliation-text").val();
            var publisherPayload = {"publisherId": publisherId, "publisherName": publisherName, "contactInformation": contactInformation, "affiliation": affiliation}
            $("#update-publisher-popup-modal").modal('toggle');
            ajaxClient.post("/library-management/update-publisher", {'publisherPayload' : JSON.stringify(publisherPayload)}, function(data){
                $("#publisher-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    publisherDetails.loadPublisherDetailsHome();
                });
            });
        });
    },
    deletePublisherDetails : function () {
        $('.delete-publisher').on('click', function () {
            var publisherInfoJson = $(this).parent().parent().find('.publisher-info-json').text().trim();
            var publisherInfo = JSON.parse(publisherInfoJson);
            $('#delete-publisher-id').text(publisherInfo.publisherId);
            $("#delete-publisher-popup-modal").modal('toggle');
        });
        $('#delete-publisher-button').on('click', function () {
            var publisherId = $("#delete-publisher-id").text();
            $("#delete-publisher-popup-modal").modal('toggle');
            
            ajaxClient.post("/library-management/delete-publisher/"+publisherId, {}, function(data){
                $("#publisher-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    publisherDetails.loadPublisherDetailsHome();
                });
            });
        });
    },
};
var authorDetails = {
    loadAuthorDetailsHome : function (){
        ajaxClient.get("/library-management/author-view", function(data){
            $("#main-content").html(data);
            initDate(365*100);
            authorDetails.addAuthorDetails();
            authorDetails.viewAuthorDetails();
            authorDetails.updateAuthorDetails();
            authorDetails.deleteAuthorDetails();
        })
    },
    addAuthorDetails : function () {
        $('#add-author-button').on('click', function () {
            var authorName = $("#author-name").val();
            if(authorName === "" || authorName === null || authorName === undefined) {
                showErrorDialogBox("Please add author name!");
                return;
            }
            var nationality = $("#author-nationality").val();
            var dateOfBirth = $("#author-birth-date").val();
            var associatedGeners = $("#author-genres").val();
            var shortBiography = $("#author-biography").val();
            
            var authorPayload = {
                "authorName": authorName, 
                "nationality": nationality, 
                "dateOfBirth": dateOfBirth ? getDate(dateOfBirth).getTime() / 1000 : null,
                "associatedGeners": associatedGeners, 
                "shortBiography": shortBiography
            }
            
            ajaxClient.post("/library-management/add-author", {'authorPayload' : JSON.stringify(authorPayload)}, function(data){
                $("#author-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    $("#author-name").val("");
                    $("#author-nationality").val("");
                    $("#author-birth-year").val("");
                    $("#author-genres").val("");
                    $("#author-biography").val("");
                    authorDetails.loadAuthorDetailsHome();
                });
            });
        });
    },
    viewAuthorDetails : function () {
        $('.view-author').on('click', function () {
            var authorInfoJson = $(this).parent().parent().find('.author-info-json').text().trim();
            var authorInfo = JSON.parse(authorInfoJson);
            $('#view-author-name').text(authorInfo.authorName);
            $('#view-author-nationality').text(authorInfo.nationality);
            $('#view-author-dob').text(getFormattedDate(authorInfo.dateOfBirth));
            $('#view-author-genres').text(authorInfo.associatedGeners);
            $('#view-author-biography').text(authorInfo.shortBiography);
            $("#view-author-popup-modal").modal('toggle');
        });
    },
    updateAuthorDetails : function () {
        $('.update-author').on('click', function () {
            var authorInfoJson = $(this).parent().parent().find('.author-info-json').text().trim();
            var authorInfo = JSON.parse(authorInfoJson);
            $('#update-author-id').text(authorInfo.authorId);
            $('#author-name-text').val(authorInfo.authorName);
            $('#author-nationality-text').val(authorInfo.nationality);
            $('#author-birth-year-text').val(getFormattedDate(authorInfo.dateOfBirth));
            $('#author-genres-text').val(authorInfo.associatedGeners);
            $('#author-biography-text').val(authorInfo.shortBiography);
            $("#update-author-popup-modal").modal('toggle');
        });
        $('#update-author-button').on('click', function () {
            var authorId = $("#update-author-id").text();
            var authorName = $("#author-name-text").val();
            if(authorName === "" || authorName === null || authorName === undefined) {
                showErrorDialogBox("Please add author name!");
                return;
            }
            var nationality = $("#author-nationality-text").val();
            var dateOfBirth = $("#author-birth-year-text").val();
            var associatedGeners = $("#author-genres-text").val();
            var shortBiography = $("#author-biography-text").val();

            if(dateOfBirth === "" || dateOfBirth === null || dateOfBirth === undefined) {
                dateOfBirth = null;
            }
            if(associatedGeners === "" || associatedGeners === null || associatedGeners === undefined) {
                associatedGeners = null;
            }
            if(shortBiography === "" || shortBiography === null || shortBiography === undefined) {
                shortBiography = null;
            }
            if(nationality === "" || nationality === null || nationality === undefined) {
                nationality = null;
            }
            var authorPayload = {
                "authorId": authorId,
                "authorName": authorName, 
                "nationality": nationality, 
                "dateOfBirth": dateOfBirth ? getDate(dateOfBirth).getTime() / 1000 : null,
                "associatedGeners": associatedGeners, 
                "shortBiography": shortBiography
            }
            $("#update-author-popup-modal").modal('toggle');
            ajaxClient.post("/library-management/update-author", {'authorPayload' : JSON.stringify(authorPayload)}, function(data){
                $("#author-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    authorDetails.loadAuthorDetailsHome();
                });
            });
        });
    },
    deleteAuthorDetails : function () {
        $('.delete-author').on('click', function () {
            var authorInfoJson = $(this).parent().parent().find('.author-info-json').text().trim();
            var authorInfo = JSON.parse(authorInfoJson);
            $('#delete-author-id').text(authorInfo.authorId);
            $("#delete-author-popup-modal").modal('toggle');
        });
        $('#delete-author-button').on('click', function () {
            var authorId = $("#delete-author-id").text();
            $("#delete-author-popup-modal").modal('toggle');
            
            ajaxClient.post("/library-management/delete-author/"+authorId, {}, function(data){
                $("#author-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    authorDetails.loadAuthorDetailsHome();
                });
            });
        });
    },
};
var vendorDetails = {
    loadVendorDetailsHome : function (){
        ajaxClient.get("/library-management/vendor-view", function(data){
            $("#main-content").html(data);
            vendorDetails.addVendorDetails();
            vendorDetails.viewVendorDetails();
            vendorDetails.updateVendorDetails();
            vendorDetails.deleteVendorDetails();
        })
    },
    addVendorDetails : function () {
        $('#add-vendor-button').on('click', function () {
            var vendorName = $("#vendor-name").val();
            if(vendorName === "" || vendorName === null || vendorName === undefined) {
                showErrorDialogBox("Please add vendor name!");
                return;
            }
            var address = $("#vendor-address").val();
            var phoneNumber = $("#vendor-phone").val();
            var email = $("#vendor-email").val();
            var gstNumber = $("#vendor-gst").val();
            
            var vendorPayload = {
                "vendorName": vendorName,
                "address": address,
                "phoneNumber": phoneNumber,
                "email": email,
                "gstNumber": gstNumber
            }
            
            ajaxClient.post("/library-management/add-vendor", {'vendorPayload' : JSON.stringify(vendorPayload)}, function(data){
                $("#vendor-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    $("#vendor-name").val("");
                    $("#vendor-address").val("");
                    $("#vendor-phone").val("");
                    $("#vendor-email").val("");
                    $("#vendor-gst").val("");
                    vendorDetails.loadVendorDetailsHome();
                });
            });
        });
    },
    updateVendorDetails : function () {
        $('.update-vendor').on('click', function () {
            var vendorInfoJson = $(this).parent().parent().find('.vendor-info-json').text().trim();
            var vendorInfo = JSON.parse(vendorInfoJson);
            $('#update-vendor-id').text(vendorInfo.vendorId);
            $('#vendor-name-text').val(vendorInfo.vendorName);
            $('#vendor-address-text').val(vendorInfo.address);
            $('#vendor-phone-text').val(vendorInfo.phoneNumber);
            $('#vendor-email-text').val(vendorInfo.email);
            $('#vendor-gst-text').val(vendorInfo.gstNumber);
            $('#vendor-account-type-text').val(vendorInfo.accountType);
            $('#vendor-account-holder-text').val(vendorInfo.accountHolderName);
            $('#vendor-account-number-text').val(vendorInfo.accountNumber);
            $('#vendor-ifsc-text').val(vendorInfo.ifscCode);
            $('#vendor-bank-name-text').val(vendorInfo.bankName);
            $("#update-vendor-popup-modal").modal('toggle');
        });
        $('#update-vendor-button').on('click', function () {
            var vendorId = $("#update-vendor-id").text();
            var vendorName = $("#vendor-name-text").val();
            if(vendorName === "" || vendorName === null || vendorName === undefined) {
                showErrorDialogBox("Please add vendor name!");
                return;
            }
            var address = $("#vendor-address-text").val();
            var phoneNumber = $("#vendor-phone-text").val();
            var email = $("#vendor-email-text").val();
            var gstNumber = $("#vendor-gst-text").val();
            var accountType = $("#vendor-account-type-text").val();
            var accountHolderName = $("#vendor-account-holder-text").val();
            var accountNumber = $("#vendor-account-number-text").val();
            var ifscCode = $("#vendor-ifsc-text").val();
            var bankName = $("#vendor-bank-name-text").val();

            if(accountType === "" || accountType === null || accountType === undefined) {
                accountType = null;
            }
            if(accountHolderName === "" || accountHolderName === null || accountHolderName === undefined) {
                accountHolderName = null;
            }
            if(accountNumber === "" || accountNumber === null || accountNumber === undefined) {
                accountNumber = null;
            }
            if(ifscCode === "" || ifscCode === null || ifscCode === undefined) {
                ifscCode = null;
            }
            if(bankName === "" || bankName === null || bankName === undefined) {
                bankName = null;
            }
            if(address === "" || address === null || address === undefined) {
                address = null;
            }
            if(phoneNumber === "" || phoneNumber === null || phoneNumber === undefined) {
                phoneNumber = null;
            }
            if(email === "" || email === null || email === undefined) {
                email = null;
            }
            if(gstNumber === "" || gstNumber === null || gstNumber === undefined) {
                gstNumber = null;
            }
            
            var vendorPayload = {
                "vendorId": vendorId,
                "vendorName": vendorName,
                "address": address,
                "phoneNumber": phoneNumber,
                "email": email,
                "gstNumber": gstNumber,
                "accountType": accountType,
                "accountHolderName": accountHolderName,
                "accountNumber": accountNumber,
                "ifscCode": ifscCode,
                "bankName": bankName
            }
            $("#update-vendor-popup-modal").modal('toggle');
            ajaxClient.post("/library-management/update-vendor", {'vendorPayload' : JSON.stringify(vendorPayload)}, function(data){
                $("#vendor-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    vendorDetails.loadVendorDetailsHome();
                });
            });
        });
    },
    deleteVendorDetails : function () {
        $('.delete-vendor').on('click', function () {
            var vendorInfoJson = $(this).parent().parent().find('.vendor-info-json').text().trim();
            var vendorInfo = JSON.parse(vendorInfoJson);
            $('#delete-vendor-id').text(vendorInfo.vendorId);
            $("#delete-vendor-popup-modal").modal('toggle');
        });
        $('#delete-vendor-button').on('click', function () {
            var vendorId = $("#delete-vendor-id").text();
            $("#delete-vendor-popup-modal").modal('toggle');
            ajaxClient.post("/library-management/delete-vendor/"+vendorId, {},function(data){
                $("#vendor-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    vendorDetails.loadVendorDetailsHome();
                });
            });
        });
    },
    viewVendorDetails : function () {
        $('.view-vendor').on('click', function () {
            var vendorInfoJson = $(this).parent().parent().find('.vendor-info-json').text().trim();
            var vendorInfo = JSON.parse(vendorInfoJson);
            $('#view-vendor-name').text(vendorInfo.vendorName);
            $('#view-vendor-address').text(vendorInfo.address);
            $('#view-vendor-phone').text(vendorInfo.phoneNumber);
            $('#view-vendor-email').text(vendorInfo.email);
            $('#view-vendor-gst').text(vendorInfo.gstNumber);
            $('#view-vendor-account-type').text(vendorInfo.accountType);
            $('#view-vendor-account-holder').text(vendorInfo.accountHolderName);
            $('#view-vendor-account-number').text(vendorInfo.accountNumber);
            $('#view-vendor-ifsc').text(vendorInfo.ifscCode);
            $('#view-vendor-bank-name').text(vendorInfo.bankName);
            $("#view-vendor-popup-modal").modal('toggle');
        });
    },
};
var genreDetails = {
    loadGenreDetailsHome : function (){
        ajaxClient.get("/library-management/genre-view", function(data){
            $("#main-content").html(data);
            genreDetails.addGenreDetails();
            genreDetails.updateGenreDetails();
            genreDetails.deleteGenreDetails();
            genreDetails.viewGenreDetails();
        })
    },
    addGenreDetails : function () {
        $('#add-genre-button').on('click', function () {
            var genreName = $("#genre-name").val();
            if(genreName === "" || genreName === null || genreName === undefined) {
                showErrorDialogBox("Please add genre name!");
                return;
            }
            var classificationNumber = $("#classification-number").val();
            
            var genrePayload = {
                "entityName": "INSTITUTE",
                "genreName": genreName,
                "classificationNumber": classificationNumber
            }
            
            ajaxClient.post("/library-management/add-genre", {'genrePayload' : JSON.stringify(genrePayload)}, function(data){
                $("#genre-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    $("#genre-name").val("");
                    $("#classification-number").val("");
                    genreDetails.loadGenreDetailsHome();
                });
            });
        });
    },
    updateGenreDetails : function () {
        $('.update-genre').on('click', function () {
            var genreInfoJson = $(this).parent().parent().find('.genre-info-json').text().trim();
            var genreInfo = JSON.parse(genreInfoJson);
            $('#update-genre-id').text(genreInfo.genreId);
            $('#genre-name-text').val(genreInfo.genreName);
            $('#classification-number-text').val(genreInfo.classificationNumber);
            $("#update-genre-popup-modal").modal('toggle');
        });
        $('#update-genre-button').on('click', function () {
            var genreId = $("#update-genre-id").text();
            var genreName = $("#genre-name-text").val();
            if(genreName === "" || genreName === null || genreName === undefined) {
                showErrorDialogBox("Please add genre name!");
                return;
            }
            var classificationNumber = $("#classification-number-text").val();
            
            var genrePayload = {
                "entityName": "INSTITUTE",
                "genreId": genreId,
                "genreName": genreName,
                "classificationNumber": classificationNumber
            }

            $("#update-genre-popup-modal").modal('toggle');
            
            ajaxClient.post("/library-management/update-genre", {'genrePayload' : JSON.stringify(genrePayload)}, function(data){
                $("#genre-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    genreDetails.loadGenreDetailsHome();
                });
            });
        });
    },
    deleteGenreDetails : function () {
        $('.delete-genre').on('click', function () {
            var genreInfoJson = $(this).parent().parent().find('.genre-info-json').text().trim();
            var genreInfo = JSON.parse(genreInfoJson);
            $('#delete-genre-id').text(genreInfo.genreId);
            $("#delete-genre-popup").modal('toggle');
        });
        $('#delete-genre-button').on('click', function () {
            var genreId = $("#delete-genre-id").text();
            $("#delete-genre-popup").modal('toggle');
            
            ajaxClient.post("/library-management/delete-genre/"+genreId, {}, function(data){
                $("#genre-status-modal-container").html(data);
                $("#configure-status-modal").modal({backdrop: 'static', keyboard: false});
                $("#configure-status-modal").off('hidden.bs.modal').on('hidden.bs.modal', function () {
                    genreDetails.loadGenreDetailsHome();
                });
            });
        });
    },
    viewGenreDetails : function () {
        $('.view-genre').on('click', function () {
            var genreInfoJson = $(this).parent().parent().find('.genre-info-json').text().trim();
            var genreInfo = JSON.parse(genreInfoJson);
            $('#view-genre-name').text(genreInfo.genreName);
            $('#view-classification-number').text(genreInfo.classificationNumber);
            $('#view-entity-name').text(genreInfo.entityName);
            $("#view-genre-popup-modal").modal('toggle');
        });
    },
};
function bookDocumentWarningPopup() {
    $("#book-document-warning-popup").modal('toggle');
}
function studentLiveSearchEvent(sessionId, searchText, resultArea, triggerMethod, status){
    ajaxClient.get("/library-management/student-live-search/"+sessionId+"?searchText=" + searchText + "&status=" + status, function(data) {
        $(resultArea).html(data);
        studentLiveSearchHandler.bindStudentSearchClickEvent(resultArea,triggerMethod);
    });
}
