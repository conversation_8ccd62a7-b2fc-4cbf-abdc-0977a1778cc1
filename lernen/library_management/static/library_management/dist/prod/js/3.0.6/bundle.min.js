var BOOK_COVER_IMAGE_SIZE_LIMIT=500,STUDENT_ENTITY="STUDENT",STUDENT_ASSIGNED_BOOK_LIST="student-book-assign-list";$(document).ready(function(){menuLoader.registerSidebarMenu(),homePage.initHomePage(),paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.addBookDetailsMenu(),menuLoader.updateBookDetailsMenu(),menuLoader.issueBookDetailsMenu(),menuLoader.libraryTypeDetailsMenu(),menuLoader.publicationDetailsMenu(),menuLoader.publisherDetailsMenu(),menuLoader.authorDetailsMenu(),menuLoader.vendorDetailsMenu(),menuLoader.genreDetailsMenu()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},addBookDetailsMenu:function(){$("#addBookNav").on("click",function(){manageBook.loadAddBookDetailsHome("")})},updateBookDetailsMenu:function(){$("#updateBookNav").on("click",function(){manageBook.loadUpdateBookDetailsHome("")})},issueBookDetailsMenu:function(){$("#issueBookNav").on("click",function(){issueBookDetailsMenu.loadMainScreen("")})},libraryTypeDetailsMenu:function(){$("#libraryTypeNav").on("click",function(){libraryTypeDetails.loadLibraryTypeDetailsHome()})},publicationDetailsMenu:function(){$("#publicationNav").on("click",function(){publicationDetails.loadPublicationDetailsHome()})},publisherDetailsMenu:function(){$("#publisherNav").on("click",function(){publisherDetails.loadPublisherDetailsHome()})},authorDetailsMenu:function(){$("#authorNav").on("click",function(){authorDetails.loadAuthorDetailsHome()})},vendorDetailsMenu:function(){$("#vendorNav").on("click",function(){vendorDetails.loadVendorDetailsHome()})},genreDetailsMenu:function(){$("#genreNav").on("click",function(){genreDetails.loadGenreDetailsHome()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)},loadHomePage:function(){ajaxClient.get("/library-management/home",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/library-management/session-home/"+e,function(e){$("#appointment-dashboard-session-content").html(e)})},refreshHomePage:function(){homePage.loadHomePageForSession()}},manageBook={loadAddBookDetailsHome:function(){ajaxClient.get("/library-management/book-view",function(e){$("#main-content").html(e),initDate(36500),manageBook.loadSingleBookTable()})},loadSingleBookTable:function(){$("#book-copies").on("change",function(){var e=$(".library-accession-number-access").text().trim(),o=$("#book-copies").val();"true"==e&&(manageBook.populateSingleBookTable(".individualBookDetails",{},o,!1),$(".row-checkbox-div").css("display","none"),$(".row-checkbox").css("display","none"),$(".select-all-checkbox").css("display","none"))})},populateSingleBookTable:function(e,o,t,a){if($("#copies-error").text(""),t<=0)$("#copies-error").text("Need greater than Zero value");else{var i=[],n=[],l=$(".vendor-details").text().trim(),r=$(".library-type-details").text().trim();l&&(i=JSON.parse(l)),r&&(n=JSON.parse(r));var s=manageBook.generateTableHeader();s+=manageBook.generateAutofillRow(i,n,a),s+=manageBook.generateTableFooter();for(var d=0;d<t;d++)0===Object.keys(o).length?s+=manageBook.generateRow(o,i,n,a):s+=manageBook.generateRow(o[d],i,n,a);s+="</tbody></table>",$(e).html(s),initDate(36500)}},generateTableHeader:function(){return'<table id="single-book-table" class="table" style="table-layout: fixed; width: 200%"><thead><tr><th style="width: 8%; text-align: center;" class="row-checkbox-div"></th><th style="width: 12%; text-align: center;">Accession Number*</th><th style="width: 8%; text-align: center;">Rack</th><th style="width: 10%; text-align: center;">Purchase Price</th><th style="width: 8%; text-align: center;">MRP</th><th style="width: 10%; text-align: center;">Bill Number</th><th style="width: 12%; text-align: center;">Date Of Purchase</th><th style="width: 10%; text-align: center;">Vendor</th><th style="width: 10%; text-align: center;">Library Type</th><th style="width: 8%; text-align: center;">Volume</th><th style="width: 12%; text-align: center;">Added On</th><th style="width: 8%; text-align: center;">Status</th><th style="width: 14%; text-align: center;">Remarks</th></tr>'},generateTableFooter:function(){return"</thead><tbody>"},generateVendorOption:function(e,o){var t='<option value="">Select Vendor</option>';return e.length>0&&e.forEach(function(e){var a=o===e.vendorId?"selected":"";t+='<option value="'+e.vendorId+'" '+a+">"+e.vendorName+"</option>"}),t},generateLibraryTypeOption:function(e,o){var t='<option value="">Select Library Type</option>';return e.length>0&&e.forEach(function(e){var a=o===e.libraryTypeId?"selected":"";t+='<option value="'+e.libraryTypeId+'" '+a+">"+e.libraryName+"</option>"}),t},generateRow:function(e,o,t,a){var i=a?"disabled":"";if(null!=e.dateOfPurchase){const o=e.dateOfPurchase,t=new Date(1e3*o),a={day:"2-digit",month:"short",year:"numeric"};e.dateOfPurchase=t.toLocaleDateString("en-GB",a).replace(","," ")}if(null!=e.addedOn){const o=e.addedOn,t=new Date(1e3*o),a={day:"2-digit",month:"short",year:"numeric"};e.addedOn=t.toLocaleDateString("en-GB",a).replace(","," ")}var n=null!=e.vendor?e.vendor.vendorId:"",l=manageBook.generateVendorOption(o,n),r=null!=e.libraryType?e.libraryType.libraryTypeId:"",s=manageBook.generateLibraryTypeOption(t,r);return'<tr class="book-details-row">    <td style="display: none;">      <p class="accessionId" style="display: none;">'+(e.accessionId||"")+'</p>    </td>    <td class="row-checkbox-div">       <input type="checkbox" class="row-checkbox">    </td>    <td>        <input type="text" class="form-control accession-number mandatory-field" value="'+(e.accessionNumber||"")+'" placeholder="accession number" '+i+'>    </td>    <td>        <input type="text" class="form-control book-rack" value="'+(e.rack||"")+'" placeholder="Rack" '+i+'>    </td>    <td>        <input type="number" class="form-control book-price" value="'+(e.purchagePrice||"")+'" placeholder="Price" '+i+'>    </td>    <td>        <input type="number" class="form-control book-mrp" value="'+(e.mrp||"")+'" placeholder="Mrp" '+i+'>    </td>    <td>        <input type="text" class="form-control book-bill-number" value="'+(e.billNumber||"")+'" placeholder="Bill Number" '+i+'>    </td>    <td>        <input type="text" class="form-control select-date book-purchase-date" value="'+(e.dateOfPurchase||"")+'" placeholder="Purchase Date" '+i+'>    </td>    <td>        <select class="custom-select mr-sm-4 book-vendor" id="book-vendor" '+i+">"+l+'        </select>    </td>    <td>        <select class="custom-select mr-sm-4 book-library-type" id="book-library-type" '+i+">"+s+'        </select>    </td>    <td>        <input type="text" class="form-control book-volume" value="'+(e.volume||"")+'" placeholder="Volume" '+i+'>    </td>    <td>        <input type="text" class="form-control select-date book-added-on-date" value="'+(e.addedOn||"")+'" placeholder="Added On Date" '+i+'>    </td>    <td>        <select class="custom-select book-status" '+i+'>            <option value="ACTIVE"'+("ACTIVE"===e.status?" selected":"")+'>ACTIVE</option>            <option value="INACTIVE"'+("INACTIVE"===e.status?" selected":"")+'>INACTIVE</option>        </select>    </td>    <td>        <textarea class="form-control book-remarks" placeholder="Remarks" '+i+">"+(e.remarks||"")+"</textarea>    </td></tr>"},generateAutofillRow:function(e,o,t){var a=t?"disabled":"";return'<tr class="autofill-row">    <td class="select-all-checkbox">         <input type="checkbox" class="select-all-checkbox" onclick="manageBook.selectAllRows(this)">    </td>     <td style="text-align: center; vertical-align: middle;">        <button id="autofill-button" class="btn btn-outline-success btn-md" style="position: relative; float: center; margin-left: 0;" onclick="manageBook.autofillFields()"'+a+'>Autofill</button>    </td>    <td class="form-group" style="text-align: center; vertical-align: middle;">        <input type="text" class="form-control default-rack" placeholder="Rack"'+a+'>    </td>    <td class="form-group" style="text-align: center; vertical-align: middle;">        <input type="number" class="form-control default-price" placeholder="Purchage Price"'+a+'>    </td>    <td class="form-group" style="text-align: center; vertical-align: middle;">        <input type="number" class="form-control default-mrp" placeholder="Mrp"'+a+'>    </td>    <td class="form-group" style="text-align: center; vertical-align: middle;">        <input type="text" class="form-control default-bill-number" placeholder="Bill Number"'+a+'>    </td>    <td class="form-group" style="text-align: center; vertical-align: middle;">        <input type="text" class="form-control select-date default-purchase-date" placeholder="Purchase Date"'+a+'>    </td>    <td class="form-group" style="text-align: center; vertical-align: middle;">        <select class="custom-select default-vendor" '+a+">"+manageBook.generateVendorOption(e,"")+'        </select>    </td>    <td class="form-group" style="text-align: center; vertical-align: middle;">        <select class="custom-select default-library-type" '+a+">"+manageBook.generateLibraryTypeOption(o,"")+'        </select>    </td>    <td style="text-align: center; vertical-align: middle;">        <input type="text" class="form-control default-volume" placeholder="Volume" '+a+'>    </td>    <td class="form-group" style="text-align: center; vertical-align: middle;">        <input type="text" class="form-control select-date default-added-on-date" placeholder="added On Date"'+a+'>    </td>    <td style="text-align: center; vertical-align: middle;">        <select class="custom-select default-status" '+a+'>            <option value="ACTIVE" selected>ACTIVE</option>            <option value="INACTIVE">INACTIVE</option>        </select>    </td>    <td>        <textarea class="form-control default-remarks" placeholder="Remarks" '+a+"></textarea>    </td></tr>"},selectAllRows:function(e){var o=e.checked;$(".row-checkbox").forEach(function(e){e.checked=o})},autofillFields:function(){var e=$(".default-rack").val(),o=$(".default-price").val(),t=$(".default-mrp").val(),a=$(".default-bill-number").val(),i=$(".default-purchase-date").val(),n=$(".default-vendor").val(),l=$(".default-library-type").val(),r=$(".default-volume").val(),s=$(".default-added-on-date").val(),d=$(".default-status").val(),u=$(".default-remarks").val();$(".book-details-row").each(function(){var c=$(this);e&&c.find(".book-rack").val(e),o&&c.find(".book-price").val(o),t&&c.find(".book-mrp").val(t),a&&c.find(".book-bill-number").val(a),i&&c.find(".book-purchase-date").val(i),n&&c.find(".book-vendor").val(n),l&&c.find(".book-library-type").val(l),r&&c.find(".book-volume").val(r),s&&c.find(".book-added-on-date").val(s),d&&c.find(".book-status").val(d),u&&c.find(".book-remarks").val(u)})},createBook:function(){if(validateMandatoryFields($("#add\\.book-info-content")))showErrorDialogBox("Invalid book information. Please try again.");else{var e="";if($("#book-cover-image")[0].files.length>0&&(e=$("#book-cover-image")[0].files[0]).size/1024>BOOK_COVER_IMAGE_SIZE_LIMIT)showErrorDialogBox("Size Of document cannot be greater than "+BOOK_COVER_IMAGE_SIZE_LIMIT+" kb");else{var o=[];if("true"==$(".library-accession-number-access").text().trim()){if(0==(t=manageBook.returnIndividualBookDetailsListWithAccess(null)))return;o=t}else{var t;if(0==(t=manageBook.returnIndividualBookDetailsListWithOutAccess(null,null,null)))return;o.push(t)}var a=$("#book-title").val().trim(),i=$("#book-number").val().trim(),n=$("#book-genre").val().trim(),l=$("#book-author").val().trim(),r=$("#book-isbn-number").val().trim(),s=$("#book-publication").val().trim(),d=$("#book-publisher").val().trim(),u=$("#book-edition").val().trim(),c=$("#book-copies").val().trim(),b=$("#book-publication-year").val().trim(),p=$("#book-language").val().trim(),m=$("#book-pages").val().trim(),k=$("#book-binding").val().trim(),v=$("#book-tags").val().trim(),g=[];if(c<=0)$("#copies-error").text("Need greater than Zero value");else{""==i&&(i=null),""==n&&(n=null),""==l&&(l=null),""==s&&(s=null),""==d&&(d=null),""==u&&(u=null),""==b&&(b=null),""==p&&(p=null),""==k&&(k=null),""==m&&(m=null),""==v&&(v=null);var f={bookId:null,genreId:n,bookTitle:a,bookNo:i,authorId:l,isbn:r,publicationId:s,publisherId:d,edition:u,noOfCopies:c,publishYear:b,language:p,typeOfBinding:k,numberOfPages:m};if(v&&v.includes(",")){var h=v.split(",").map(e=>e.trim());g=new Set(h)}else v&&(g=new Set([v.trim()]));var y={bookDetailPayload:f,tags:Array.from(g),individualBookDetailsPayloads:o},D=new FormData;""!=e&&(D.append("document",e),D.append("documentName","BOOK_COVER_IMAGE")),D.append("add_book_details",JSON.stringify(y)),ajaxClient.uploadFile("/library-management/add-new-book",D,function(e){$("#book-status-modal-container").html(e),"True"==$("#success").text().trim()?(manageBook.loadUpdateBookDetailsHome(),$("#updateBookNav").parent().addClass("active"),$("#addBookNav").parent().removeClass("active")):$("#book-status-modal").modal("toggle")})}}}},returnIndividualBookDetailsListWithOutAccess:function(e,o,t){var a=$("#book-rack").val().trim();""==a&&(a=null);var i=$("#book-price").val().trim();""==i&&(i=null);var n=$("#book-mrp").val().trim();""==n&&(n=null);var l=$("#book-bill-number").val().trim();""==l&&(l=null);var r=null,s=getDate($(".book-purchase-date").val());null!=s&&(r=s.getTime()/1e3);var d=$("#book-vendor").val().trim();""==d&&(d=null);var u=$("#book-library-type").val().trim();""==u&&(u=null);var c=$("#book-volume").val().trim();""==c&&(c=null);var b=null,p=getDate($(".book-added-on-date").val());null!=p&&(b=p.getTime()/1e3);var m=$("#book-status").val();if(""==m)return showErrorDialogBox("Invalid Status of book "),!1;var k=$("#book-remarks").val();return""==k&&(k=null),{accessionId:e,bookId:o,accessionNumber:t,rack:a,purchasePrice:i,mrp:n,billNumber:l,dateOfPurchase:r,vendorId:d,status:m,volume:c,addedOn:b,libraryTypeId:u,remarks:k}},returnIndividualBookDetailsListWithAccess:function(e){return individualBookDetailList=[],$(".book-details-row").each(function(){if(validateMandatoryFields($(this)))return showErrorDialogBox("Invalid book information. Please try again."),!1;var o=$(this).find(".accessionId").text().trim();""==o&&(o=null);var t=$(this).find(".accession-number").val().trim();if(""==t)return showErrorDialogBox("Invalid Accession Number Can't be Empty"),!1;var a=$(this).find(".book-rack").val().trim();""==a&&(a=null);var i=$(this).find(".book-price").val().trim();i=""==i?null:parseFloat(i);var n=$(this).find(".book-mrp").val().trim();n=""==n?null:parseFloat(n);var l=$(this).find(".book-bill-number").val().trim();""==l&&(l=null);var r=null,s=getDate($(this).find(".book-purchase-date").val());null!=s&&(r=s.getTime()/1e3);var d=$(this).find(".book-vendor").val().trim();""==d&&(d=null);var u=$(this).find(".book-library-type").val().trim();""==u&&(u=null);var c=$(this).find(".book-volume").val().trim();""==c&&(c=null);var b=null,p=getDate($(this).find(".book-added-on-date").val());null!=p&&(b=p.getTime()/1e3);var m=$(this).find(".book-status").val();if(""==m)return showErrorDialogBox("Invalid Status of book "),!1;var k=$(this).find(".book-remarks").val();""==k&&(k=null);var v={accessionId:o,bookId:e,accessionNumber:t,rack:a,purchasePrice:i,mrp:n,billNumber:l,dateOfPurchase:r,vendorId:d,status:m,volume:c,addedOn:b,libraryTypeId:u,remarks:k};individualBookDetailList.push(v)}),individualBookDetailList},back:function(){manageBook.loadUpdateBookDetailsHome("")},loadUpdateBookDetailsHome:function(){ajaxClient.get("/library-management/update-book-View",function(e){$("#main-content").html(e),initDate(36500),$("#searchbooks").length>0?(manageBook.searchBook(!0),manageBook.registerBookSearchCallback()):console.error("Search button does not exist after content load.")})},registerBookSearchCallback:function(){$("#searchbooks").on("click",function(){manageBook.searchBook(!0)}),$("#searchBookInput").on("keyup",function(e){13==e.keyCode&&manageBook.searchBook(!0)})},searchBook:function(e){var o=$("#searchBookInput").val();null==o&&(o="");var t=$(".page-item.active").find(".page-number").text().trim();e&&(t=1);var a=$("#items-per-page").val();null!=t&&""!=t||(t=1);var i=(t-1)*a;$("#searchBookResult").html(""),ajaxClient.get("/library-management/book-details/"+i+"/"+a+"?text="+o,function(e){$("#searchBookResult").html(e);var o=$("#pagination-info").text().trim();if(o)try{var t=JSON.parse(o);$("#items-per-page").val(t.itemsPerPage),$(".page-item").removeClass("active");var a=t.offset/t.itemsPerPage+1;$("#page-number-"+a).addClass("active")}catch(e){console.error("Error parsing JSON: ",e)}else console.error("Pagination info is empty.");manageBook.registerDeleteBookDeatilsCallBack(),manageBook.registerUpdateBookDetailsCallBack(),manageBook.registerUpdateNumberOfBookCallBack(),manageBook.registerViewBookDetailsCallBack(),manageBook.registerUploadBookDocumentCallBack(),bookList.initPagination()})},registerViewBookDetailsCallBack:function(){$(".view-book-info").on("click",function(){var e=$(this).parent().parent().find(".book-info-json").text().trim(),o=JSON.parse(e),t=$(".library-accession-number-access").text().trim();if($("#book-id").text(o.bookId),$("#book-page-text").text("View Book"),$("#update-book-button").css("display","none"),$("#book-title").val(o.bookTitle),$("#book-title").prop("disabled",!0),$("#book-number").val(null==o.bookNo?"":o.bookNo),$("#book-number").prop("disabled",!0),$("#book-genre").val(null==o.genre?"":o.genre.genreId),$("#book-genre").prop("disabled",!0),$("#book-author").val(null==o.author?"":o.author.authorId),$("#book-author").prop("disabled",!0),$("#book-isbn-number").val(o.isbn),$("#book-isbn-number").prop("disabled",!0),$("#book-publication").val(null==o.publication?"":o.publication.publicationId),$("#book-publication").prop("disabled",!0),$("#book-publisher").val(null==o.publisher?"":o.publisher.publisherId),$("#book-publisher").prop("disabled",!0),$("#book-edition").val(null==o.edition?"":o.edition),$("#book-edition").prop("disabled",!0),$("#book-copies").val(o.noOfCopies),$("#book-copies").prop("disabled",!0),$("#book-publication-year").val(null==o.publishYear?"":o.edition),$("#book-publication-year").prop("disabled",!0),$("#book-language").val(null==o.language?"":o.language),$("#book-language").prop("disabled",!0),$("#book-binding").val(null==o.typeOfBinding?"":o.typeOfBinding),$("#book-binding").prop("disabled",!0),$("#book-pages").val(0==o.numberOfPages?"":o.numberOfPages),$("#book-pages").prop("disabled",!0),$("#book-tags").val(null==o.tags?"":o.tags),$("#book-tags").prop("disabled",!0),null!=o.coverImage?$("#update-book-photo-label").html("Upload Photo <span style='color:green;'> (Image already uploaded) </span>"):$("#update-book-photo-label").html("Upload Photo <span style='color:red;'> (Image not uploaded yet) </span>"),"true"==t)manageBook.populateSingleBookTable(".individualBookDetails",o.individualBookDetails,o.noOfCopies,!0),$(".row-checkbox-div").css("display","none"),$(".row-checkbox").css("display","none"),$(".select-all-checkbox").css("display","none");else{if(individualBookDetail=o.individualBookDetails[0],null!=individualBookDetail.dateOfPurchase){const e=getFormattedDate(individualBookDetail.dateOfPurchase);$(".book-purchase-date").val(e)}$("#book-bill-number").val(null==individualBookDetail.billNumber?"":individualBookDetail.billNumber),$("#book-bill-number").prop("disabled",!0),$(".book-purchase-date").prop("disabled",!0),$("#book-vendor").val(null==individualBookDetail.vendor?"":individualBookDetail.vendor.vendorId),$("#book-vendor").prop("disabled",!0),$("#book-price").val(0==individualBookDetail.purchagePrice?"":individualBookDetail.purchagePrice),$("#book-price").prop("disabled",!0),$("#book-rack").val(null==individualBookDetail.rack?"":individualBookDetail.rack),$("#book-rack").prop("disabled",!0),$("#book-library-type").val(null==individualBookDetail.libraryType?"":individualBookDetail.libraryType.libraryTypeId),$("#book-library-type").prop("disabled",!0),$("#book-volume").val(null==individualBookDetail.volume?"":individualBookDetail.volume),$("#book-volume").prop("disabled",!0),$("#book-status").val(individualBookDetail.status),$("#book-status").prop("disabled",!0),$("#book-remark").val(null==individualBookDetail.remarks?"":individualBookDetail.remarks),$("#book-remark").prop("disabled",!0)}$(".book-details-screen").attr("style","display:none"),$("#book-list\\.update-book-screen").attr("style","display:block")})},registerDeleteBookDeatilsCallBack:function(){$(".delete-book-info").on("click",function(){var e=$(this).parent().parent().find(".book-info-json").text().trim(),o=JSON.parse(e).bookId;$("#book-delete-confirm-button").attr("onclick","manageBook.deleteBookDetails('"+o+"')"),$("#book-delete-warning-popup").modal("toggle")})},registerUpdateNumberOfBookCallBack:function(){$(".add-number-of-copies").on("click",function(){$("#popup-modal-type-for-book-details").text("Add Number Of Copies"),$(".update-book-detail-button").text("Add Copies");var e=$(this).parent().parent().parent().find(".book-info-json").text().trim(),o=JSON.parse(e),t=o.bookId,a=$(".library-accession-number-access").text().trim();if("true"==a)$("#update-book-copies").on("change",function(){var e=$("#update-book-copies").val();a&&e>0?($("#update-copies-error").text(""),manageBook.populateSingleBookTable(".update-book-detail-popup",{},e,!1),$(".row-checkbox-div").css("display","none"),$(".row-checkbox").css("display","none"),$(".select-all-checkbox").css("display","none")):($("#update-copies-error").text("Number of copies Can't be zero"),$(".update-book-detail-popup").html(""))});else{if(individualBookDetail=o.individualBookDetails[0],null!=individualBookDetail.dateOfPurchase){const e=getFormattedDate(individualBookDetail.dateOfPurchase);$(".update-book-purchase-date").val(e)}$("#update-book-bill-number").val(null==individualBookDetail.billNumber?"":individualBookDetail.billNumber),$("#update-book-vendor").val(null==individualBookDetail.vendor?"":individualBookDetail.vendor.vendorId),$("#update-book-price").val(0==individualBookDetail.purchagePrice?"":individualBookDetail.purchagePrice),$("#update-book-rack").val(null==individualBookDetail.rack?"":individualBookDetail.rack),$("#update-book-library-type").val(null==individualBookDetail.libraryType?"":individualBookDetail.libraryType.libraryTypeId),$("#update-book-volume").val(null==individualBookDetail.volume?"":individualBookDetail.volume),$("#update-book-status").val(individualBookDetail.status),$("#update-book-remark").val(null==individualBookDetail.remarks?"":individualBookDetail.remarks)}$(".update-book-detail-button").attr("onclick","manageBook.updateNumberOfBookDetails('"+t+"')"),$("#update-book-details-modal").modal("toggle")}),$(".delete-number-of-copies").on("click",function(){$("#add-number-of-book-access-number").css("display","none"),$("#popup-modal-type-for-book-details").text("Delete Number Of Copies"),$(".update-book-detail-button").text("Delete Copies");var e=$(this).parent().parent().parent().find(".book-info-json").text().trim(),o=(t=JSON.parse(e)).bookId;if("true"==$(".library-accession-number-access").text().trim()){$(".update-book-copies-div").css("display","none");e=$(this).parent().parent().parent().find(".book-info-json").text().trim();var t=JSON.parse(e);manageBook.populateSingleBookTable(".update-book-detail-popup",t.individualBookDetails,t.noOfCopies,!1)}$(".update-book-detail-button").attr("onclick","manageBook.updateNumberOfBookDetails('"+o+"')"),$("#update-book-details-modal").modal("toggle")}),$("#update-book-details-modal").on("hidden.bs.modal",function(){$("#add-number-of-book-access-number").css("display",""),$(".update-book-copies-div").css("display","")})},updateNumberOfBookDetails:function(e){var o=$("#popup-modal-type-for-book-details").text().trim();$("#update-copies-error").text("");var t=$(".library-accession-number-access").text().trim(),a={bookId:e,dataUpdationAction:""};if("Add Number Of Copies"===o){var i=$("#update-book-copies").val();if(i<=0)return $("#update-copies-error").text("Number of copies can't be zero"),!1;var n="true"===t?manageBook.returnIndividualBookDetailsListWithAccess(e):[];if("true"===t&&!1===n)return;if("true"!==t){var l=(e,o=null)=>{var t=$(e).val().trim();return""===t?o:t},r=getDate($(".update-book-purchase-date").val());if(n.push({accessionId:null,bookId:e,accessionNumber:null,rack:l("#update-book-rack"),price:l("#update-book-price"),billNumber:l("#update-book-bill-number"),dateOfPurchase:null==r?null:r.getTime()/1e3,vendorId:l("#update-book-vendor"),status:l("#update-book-status"),volume:l("#update-book-volume"),libraryTypeId:l("#update-book-library-type"),remarks:l("#update-book-remarks")}),!n[0].status)return showErrorDialogBox("Invalid Status of book"),!1}a.numberOfCopies=i,a.individualBookDetailsPayloads=n,a.dataUpdationAction="ADD"}else{var s=[];if("true"===t){if($(".book-details-row").each(function(){$(this).find(".row-checkbox").prop("checked")&&s.push($(this).find(".accessionId").text().trim())}),0===s.length)return showErrorDialogBox("At least select one of the checkboxes."),!1;a.numberOfCopies=s.length,a.accessionIdList=s}else a.numberOfCopies=i;a.dataUpdationAction="DELETE"}ajaxClient.post("/library-management/update-number-of-book",JSON.stringify(a),function(e){$("#main-book-status-modal-container").html(e),$("#update-book-details-modal").modal("hide"),"True"===$("#success").text().trim()?manageBook.loadUpdateBookDetailsHome():$("#book-update-status-modal").modal({backdrop:"static",keyboard:!1}).modal("toggle")})},registerUpdateBookDetailsCallBack:function(){$(".update-book-info").on("click",function(){var e=$(this).parent().parent().parent().find(".book-info-json").text().trim(),o=JSON.parse(e),t=$(".library-accession-number-access").text().trim();if($("#book-id").text(o.bookId),$("#book-title").val(o.bookTitle),$("#book-number").val(null==o.bookNo?"":o.bookNo),$("#book-genre").val(null==o.genre?"":o.genre.genreId),$("#book-author").val(null==o.author?"":o.author.authorId),$("#book-isbn-number").val(o.isbn),$("#book-publication").val(null==o.publication?"":o.publication.publicationId),$("#book-publisher").val(null==o.publisher?"":o.publisher.publisherId),$("#book-edition").val(null==o.edition?"":o.edition),$("#book-copies").val(o.noOfCopies),$("#book-copies").prop("disabled",!0),$("#book-publication-year").val(null==o.publishYear?"":o.edition),$("#book-language").val(null==o.language?"":o.language),$("#book-binding").val(null==o.typeOfBinding?"":o.typeOfBinding),$("#book-pages").val(0==o.numberOfPages?"":o.numberOfPages),$("#book-tags").val(null==o.tags?"":o.tags),null!=o.coverImage?$("#update-book-photo-label").html("Upload Photo <span style='color:green;'> (Image already uploaded) </span>"):$("#update-book-photo-label").html("Upload Photo <span style='color:red;'> (Image not uploaded yet) </span>"),"true"==t)manageBook.populateSingleBookTable(".individualBookDetails",o.individualBookDetails,o.noOfCopies,!1),$(".row-checkbox-div").css("display","none"),$(".row-checkbox").css("display","none"),$(".select-all-checkbox").css("display","none");else{if(individualBookDetail=o.individualBookDetails[0],null!=individualBookDetail.dateOfPurchase){const e=getFormattedDate(individualBookDetail.dateOfPurchase);$(".book-purchase-date").val(e)}$("#book-bill-number").val(null==individualBookDetail.billNumber?"":individualBookDetail.billNumber),$("#book-vendor").val(null==individualBookDetail.vendor?"":individualBookDetail.vendor.vendorId),$("#book-price").val(0==individualBookDetail.purchagePrice?"":individualBookDetail.purchagePrice),$("#book-rack").val(null==individualBookDetail.rack?"":individualBookDetail.rack),$("#book-library-type").val(null==individualBookDetail.libraryType?"":individualBookDetail.libraryType.libraryTypeId),$("#book-volume").val(null==individualBookDetail.volume?"":individualBookDetail.volume),$("#book-status").val(individualBookDetail.status),$("#book-remark").val(null==individualBookDetail.remarks?"":individualBookDetail.remarks)}$(".book-details-screen").attr("style","display:none"),$("#book-list\\.update-book-screen").attr("style","display:block")})},registerUploadBookDocumentCallBack:function(){$(".upload-book-document").on("click",function(){var e=$(this).parent().parent().find(".book-info-json").text().trim(),o=JSON.parse(e);$("#upload-document-book-id").text(o.bookId),$(".book-details-screen").attr("style","display:none"),$("#book-list\\.upload-document-screen").attr("style","display:block"),$("#upload-document-book-title").text(o.bookTitle),manageBook.populateUploadedDocuments(o.bookDocument)})},returnToMainScreen:function(){manageBook.searchBook(!0),$(".book-details-screen").attr("style","display:none"),$("#book-details").attr("style","display:block")},deleteBookDetails:function(e){ajaxClient.post("/library-management/delete-book/"+e,{},function(e){$("#main-book-status-modal-container").html(e),$("#book-delete-warning-popup").modal("hide"),"True"==$("#success").text().trim()?manageBook.loadUpdateBookDetailsHome():$("#book-update-status-modal").modal("toggle")})},updateBookDetails:function(){if(validateMandatoryFields($("#update\\.book-info-content")))showErrorDialogBox("Invalid book information. Please try again.");else{var e=[],o=$(".library-accession-number-access").text().trim(),t=$("#book-id").text().trim(),a=$("#book-copies").val().trim();if("true"==o){if(0==(r=manageBook.returnIndividualBookDetailsListWithAccess(t)))return;e=r}else for(var i=$("#book-details-json").text().trim(),n=JSON.parse(i)[0].individualBookDetails,l=0;l<a;l++){var r,s=n[l];if(0==(r=manageBook.returnIndividualBookDetailsListWithOutAccess(s.accessionId,t,s.accessionNumber)))return;e.push(r)}var d=$("#book-title").val().trim(),u=$("#book-number").val().trim(),c=$("#book-genre").val().trim(),b=$("#book-author").val().trim(),p=$("#book-isbn-number").val().trim(),m=$("#book-publication").val().trim(),k=$("#book-publisher").val().trim(),v=$("#book-edition").val().trim(),g=$("#book-publication-year").val().trim(),f=$("#book-language").val().trim(),h=$("#book-pages").val().trim(),y=$("#book-binding").val().trim(),D=$("#book-tags").val().trim(),B=[];if(a<=0)$("#copies-error").text("Need greater than Zero value");else{""==u&&(u=null),""==c&&(c=null),""==b&&(b=null),""==m&&(m=null),""==k&&(k=null),""==v&&(v=null),""==g&&(g=null),""==f&&(f=null),""==y&&(y=null),""==h&&(h=null),""==D&&(D=null);var x={bookId:t,genreId:c,bookTitle:d,bookNo:u,authorId:b,isbn:p,publicationId:m,publisherId:k,edition:v,noOfCopies:a,publishYear:g,language:f,typeOfBinding:y,numberOfPages:h};if(D&&D.includes(",")){var N=D.split(",").map(e=>e.trim());B=new Set(N)}else D&&(B=new Set([D.trim()]));var S={bookDetailPayload:x,tags:Array.from(B),individualBookDetailsPayloads:e};ajaxClient.post("/library-management/update-book",JSON.stringify(S),function(e){$("#book-status-modal-container").html(e),$("#book-document-warning-popup").modal("hide"),"True"==$("#success").text().trim()?($("#book-list\\.update-book-screen").attr("style","display:none"),$(".book-details-screen").attr("style","display:block"),manageBook.loadUpdateBookDetailsHome()):($("#book-update-status-modal").modal("toggle"),$("#book-update-status-modal").modal({backdrop:"static",keyboard:!1}))})}}},resetNewDocumentUploadPopup:function(){$("#upload-document-type").val(""),$("#upload-document-file").val(""),$("#upload-document-file-label").text(""),$("#upload-document-name").val(""),registerUploadFileCallback(),$("#upload-new-document-modal").modal({backdrop:"static",keyboard:!1})},populateUploadedDocuments:function(e){if(null!==e&&0!=e.length){for(var o="<br>",t=0,a=0;a<e.length;a++){var i=e[a];if(null!=i.documentId&&"BOOK_COVER_IMAGE_THUMBNAIL"!=i.documentType){t%3==0&&(0!=t&&(o+="</div>"),o+='<div class="row">'),t++;var n="Uploaded on : "+getFormattedDate(i.uploadTime);o+='<div class="col-sm-4"> <div class="card bg-light text-center"> <div class="card-header"> <h5> <strong> '+i.documentName+' </strong></h5> </div> <div class="card-body"> <p style="display:none;" class="view-document-id"> '+i.documentId+' </p> <p class="card-text"> Category : '+i.documentTypeDisplayName+' </p> <a href="#" class="btn btn-outline-info download-document">Download </a> <a href="#" class="btn btn-outline-danger delete-document">Delete </a> </div> <div class="card-footer text-muted"> '+n+" </div> </div> </div>"}}o+="</div> <br>",$("#uploaded-documents").html(o),manageBook.bindDocumentActions()}else $("#uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>")},bindDocumentActions:function(){$(".download-document").on("click",function(){var e=$(this).parent().find("p.view-document-id").text().trim(),o=$("#upload-document-book-id").text().trim();window.open(baseURL+"/library-management/document-download/"+e+"/"+o,"_blank")}),$(".delete-document").on("click",function(){var e=$(this).parent().find("p.view-document-id").text().trim(),o=$("#upload-document-book-id").text().trim();$("#document-delete-confirm-button").attr("onclick","manageBook.deleteDocument('"+e+"','"+o+"')"),$("#document-delete-confirm-modal").modal({backdrop:"static",keyboard:!1})})},uploadDocument:async function(){var e=$("#upload-document-type option:selected").val().trim(),o=$("#upload-document-book-id").text().trim();if(""!=e){var t;if($("#upload-document-file")[0].files.length>0){var a=$("#upload-document-file")[0].files[0];if((t=await compressFileUtils.compress(a)).size/1024>150)showErrorDialogBox("Size Of document cannot be greater than 150 kb");else{var i=$("#upload-document-book-title").text(),n=new FormData;n.append("document",t),n.append("documentType",e),n.append("documentName",i),$("#upload-new-document-modal").modal("toggle"),ajaxClient.uploadFile("/library-management/document-upload/"+o,n,function(e){$("#manage-document-upload-status-modal-container").html(e),$("#book-document-upload-status-modal").modal({backdrop:"static",keyboard:!1});var o=$("#success-document-upload-response").text().trim(),t=JSON.parse(o);manageBook.populateUploadedDocuments(t)})}}else showErrorDialogBox("No file selected. Please choose a document to upload")}else showErrorDialogBox("Document type field is mandatory please fill it then proceed.")},deleteDocument:function(e,o){ajaxClient.post("/library-management/document-delete/"+e+"/"+o,{},function(e){$("#manage-document-upload-status-modal-container").html(e),$("#book-document-upload-status-modal").modal({backdrop:"static",keyboard:!1});var o=$("#success-document-upload-response").text().trim(),t=JSON.parse(o);manageBook.populateUploadedDocuments(t)})}},issueBookDetailsMenu={dataCache:{},loadMainScreen:function(){ajaxClient.get("/library-management/issued-book",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(issueBookDetailsMenu.changeSession),issueBookDetailsMenu.bindSearchStudentBookAssignmentEvent()})},doneStudentSearchTyping:function(e){var o=$("#book-assign-search-text").val().trim();studentLiveSearchEvent(academicSessionHandler.getSelectedSessionId(),o,e,issueBookDetailsMenu.loadStudentBookAssignment,"ENROLLED")},loadStudentBookAssignment:function(e){var o=academicSessionHandler.getSelectedSessionId();issueBookDetailsMenu.loadEntityBookAssignment(o,STUDENT_ENTITY,e,STUDENT_ASSIGNED_BOOK_LIST,!0,"ISSUED")},loadEntityBookAssignment:function(e,o,t,a,i,n){if(o=o.trim(),t=t.trim(),""!=e&&""!=o){var l=$(".page-item.active").find(".page-number").text().trim();i&&(l=1);var r=$("#items-per-page").val();null!=l&&""!=l||(l=1);var s=(l-1)*r;ajaxClient.get("/library-management/student-book-details/"+e+"/"+o+"/"+t+"?offset="+s+"&limit="+r+"&status="+n,function(i){$("#"+a).html(i);var l=$("#pagination-info").text().trim();if(l)try{var r=JSON.parse(l);$("#items-per-page").val(r.itemsPerPage),$(".page-item").removeClass("active");var s=r.offset/r.itemsPerPage+1;$("#page-number-"+s).addClass("active")}catch(e){console.error("Error parsing JSON: ",e)}else console.error("Pagination info is empty.");"ISSUED"==n?($("#nav-issued-book-tab").addClass("active"),$("#nav-returned-book-tab").removeClass("active"),$("#issued-book-content").addClass("show active"),$("#return-book-content").removeClass("show active")):"RETURNED"==n?($("#nav-returned-book-tab").addClass("active"),$("#nav-issued-book-tab").removeClass("active"),$("#return-book-content").addClass("show active"),$("#issued-book-content").removeClass("show active")):($("#nav-lost-book-tab").addClass("active"),$("#nav-issued-book-tab").removeClass("active"),$("#nav-returned-book-tab").removeClass("active"),$("#lost-book-content").addClass("show active"),$("#issued-book-content").removeClass("show active"),$("#return-book-content").removeClass("show active")),bookList.bookDetailsInitPagination(e,o,t,a,n),issueBookDetailsMenu.getLibraryBookTransactions(),issueBookDetailsMenu.getLibraryNewBookIssueTransactions(),issueBookDetailsMenu.getLibraryReturnBookTransactions(),issueBookDetailsMenu.registerViewBookDetailsCallBack(n),issueBookDetailsMenu.registerDeleteBookDeatilsCallBack(),issueBookDetailsMenu.registerUpdateBookDeatilsCallBack()})}else showErrorDialogBox("Please select entity and session to assign fees.")},changeSession:function(){var e=academicSessionHandler.getSelectedSession();$("#academic-year-display").text(e.displayName)},registerViewBookDetailsCallBack:function(e){$(".view-book-info").on("click",function(){var o=$(this).closest("tr").find(".student-book-info").text().trim();try{var t=JSON.parse(o);$(".basic-info-tags").css("display","none"),$("#detail-view\\.book-title").text(t.bookDetails.bookTitle),$("#detail-view\\.book-genre").text(null!=t.bookDetails.genre?t.bookDetails.genre.genreName:"N/A"),$("#detail-view\\.book-author").text(null!=t.bookDetails.author?t.bookDetails.author.authorName:"N/A"),$("#detail-view\\.book-isbn-number").text(t.bookDetails.isbn||"N/A"),$("#detail-view\\.book-publication").text(null!=t.bookDetails.publication?t.bookDetails.publication.publicationName:"N/A"),$("#detail-view\\.book-edition").text(t.bookDetails.edition||"N/A"),$("#detail-view\\.book-publisher").text(null!=t.bookDetails.publisher?t.bookDetails.publisher.publisherName:"N/A"),$("#detail-view\\.book-publication-year").text(t.bookDetails.publishYear>0?t.bookDetails.publishYear:"N/A"),$("#detail-view\\.book-language").text(t.bookDetails.language||"N/A"),$("#detail-view\\.book-rack").text(t.bookDetails.individualBookDetail.rack||"N/A"),$("#detail-view\\.book-price").text(t.bookDetails.individualBookDetail.purchagePrice||"N/A"),$("#detail-view\\.book-number").text(t.bookDetails.individualBookDetail.accessionNumber||"N/A"),$("#detail-view\\.book-issue-date").text(getFormattedDate(t.issuedBookEntry.issuedAt)||"N/A"),$("#detail-view\\.book-issued-by").text(t.issuedBookEntry.issuedBy.name||"N/A"),$("#detail-view\\.book-due-date").text(getFormattedDate(t.issuedBookEntry.dueDate)||"N/A"),$("#detail-view\\.book-return-date").text(getFormattedDate(t.issuedBookEntry.returnedAt)||"N/A"),$("#detail-view\\.book-received-by").text(t.issuedBookEntry.receivedBy?t.issuedBookEntry.receivedBy.name:"N/A"),"ISSUED"===e?$("#view-book-issued-details-modal").modal("toggle"):"RETURNED"===e?$("#view-book-returned-details-modal").modal("toggle"):$("#view-book-lost-details-modal").modal("toggle")}catch(e){console.error("Error parsing JSON for book details:",e)}})},registerDeleteBookDeatilsCallBack:function(){$(".delete-book-info").on("click",function(){var e=academicSessionHandler.getSelectedSessionId(),o=$(this).closest("tr").find(".student-book-info").text().trim(),t=JSON.parse(o),a=t.issuedBookEntry.transactionId,i=t.issuedBookEntry.status,n=$("#book-assign-view-student-id").text().trim();$("#book-delete-confirm-button").attr("onclick","issueBookDetailsMenu.deleteBookDetails('"+e+"','"+a+"','"+i+"','"+n+"')"),$("#book-delete-warning-popup").modal("toggle")})},registerUpdateBookDeatilsCallBack:function(){$(".update-book-info").on("click",function(){var e=academicSessionHandler.getSelectedSessionId(),o=$(this).closest("tr").find(".student-book-info").text().trim(),t=JSON.parse(o),a=t.issuedBookEntry.transactionId,i=t.issuedBookEntry.status,n=$("#book-assign-view-student-id").text().trim();$("#book-date-update-button").attr("onclick","issueBookDetailsMenu.updateBookDetails('"+e+"','"+a+"','"+i+"','"+n+"')"),"ISSUED"==i?($("#book-date-update-popup-text").text("Issued Book Update Date"),$("#book-date-update-text").text("Issued Date*")):"RETURNED"==i?($("#book-date-update-popup-text").text("Returned Book Update Date"),$("#book-date-update-text").text("Return Date*")):($("#book-date-update-popup-text").text("Lost Book Update Date"),$("#book-date-update-text").text("Lost Date*")),$("#book-date-update-popup").modal("toggle"),initDate(36500),$(".book-date-update").val(getTodayDateInInputFormat())})},deleteBookDetails:function(e,o,t,a){ajaxClient.post("/library-management/delete-issued-Book-data/"+e+"/"+o,{},function(o){$("#library-book-status-modal-container").html(o),$("#book-delete-warning-popup").modal("hide"),"True"==$("#success").text().trim()?issueBookDetailsMenu.loadEntityBookAssignment(e,STUDENT_ENTITY,a,STUDENT_ASSIGNED_BOOK_LIST,!0,t):$("#issue-book-status-modal").modal("toggle")})},updateBookDetails:function(e,o,t,a){var i=0;$("div.bookDateUpdate").find("input.book-date-update").length>0&&(i=getDate($("div.bookDateUpdate").find("input.book-date-update").val()).getTime()/1e3),0!=i?($("#book-date-update-popup").modal("toggle"),ajaxClient.post("/library-management/update-issued-book-date/"+e+"/"+o+"?status="+t+"&update_issue_book_date="+i,{},function(o){$("#library-book-status-modal-container").html(o),"True"==$("#success").text().trim()?issueBookDetailsMenu.loadEntityBookAssignment(e,STUDENT_ENTITY,a,STUDENT_ASSIGNED_BOOK_LIST,!0,t):$("#issue-book-status-modal").modal("toggle")})):alert("Please select Book Date!")},bindSearchStudentBookAssignmentEvent:function(){var e="#book-assign-student-search-result";$("#book-assign-search-student").on("click",function(){issueBookDetailsMenu.doneStudentSearchTyping(e)}),$("#book-assign-search-text").on("keyup",function(o){13==o.keyCode&&issueBookDetailsMenu.doneStudentSearchTyping(e)}),liveSearchHandler.bindEvent("#book-assign-search-text",e,issueBookDetailsMenu.doneStudentSearchTyping)},getLibraryBookTransactions:function(){var e=$("#book-assign-view-student-id").text().trim(),o=academicSessionHandler.getSelectedSessionId();$("#nav-issued-book-tab").on("click",function(){issueBookDetailsMenu.loadEntityBookAssignment(o,STUDENT_ENTITY,e,STUDENT_ASSIGNED_BOOK_LIST,!0,"ISSUED")}),$("#nav-returned-book-tab").on("click",function(){issueBookDetailsMenu.loadEntityBookAssignment(o,STUDENT_ENTITY,e,STUDENT_ASSIGNED_BOOK_LIST,!0,"RETURNED")}),$("#nav-lost-book-tab").on("click",function(){issueBookDetailsMenu.loadEntityBookAssignment(o,STUDENT_ENTITY,e,STUDENT_ASSIGNED_BOOK_LIST,!0,"LOST")})},getLibraryNewBookIssueTransactions:function(){var e=$("#book-assign-view-student-id").text().trim(),o=academicSessionHandler.getSelectedSessionId();$("#book-issue-modal-student-id").text(e),$("#book-issue-modal-session-id").text(o),$("#student-assign-new-book-button").on("click",function(){$("#book-issue-modal").modal("toggle"),issueBookDetailsMenu.bindSearchBookEvent()})},getLibraryReturnBookTransactions:function(){$("#student-return-book-button").on("click",function(){var e=issueBookDetailsMenu.getSelectedBulkTransactionIds(),o=academicSessionHandler.getSelectedSessionId(),t=$("#book-assign-view-student-id").text().trim();null==issueBookDetailsMenu.dataCache.selectedIssuedBookCount&&(issueBookDetailsMenu.dataCache.selectedIssuedBookCount=0),$("#book-Return-confirm-popup-text").text("Total Number Of Book Return : "+issueBookDetailsMenu.dataCache.selectedIssuedBookCount),$("#book-return-conformation-popup").modal("toggle"),$(".book-Return-date").val(getTodayDateInInputFormat()),initDate(36500),issueBookDetailsMenu.returnBookDetails(o,t,e)})},bindSearchBookEvent:function(){$("#books-search").on("keyup",function(e){13==e.keyCode&&issueBookDetailsMenu.doneBookSearchTyping("#books-search-result")}),liveSearchHandler.bindEvent("#books-search","#books-search-result",issueBookDetailsMenu.doneBookSearchTyping)},doneBookSearchTyping:function(e){var o=$("#books-search").val().trim();issueBookDetailsMenu.bookLiveSearchEvent(o,e)},bookLiveSearchEvent:function(e,o){ajaxClient.get("/library-management/book-live-search?searchText="+e,function(e){$(o).html(e),issueBookDetailsMenu.loadBookDetails()})},loadBookDetails:function(){$("#live-search-book-results tbody tr td").on("click",function(){var e=JSON.parse($(this).parent().find("td.book-info-td").text().trim());issueBookDetailsMenu.addSelectedBookRow(e,!0,!1,!0)})},addSelectedBookRow:function(e,o,t,a){var i=e.bookTitle,n=e.isbn,l=e.bookId,r=e.individualBookDetailLites,s=$("accession-number-access").text(),d=($(this).parent(),0),u="";o&&(u=' <button type="button" class="close delete-book-row " aria-label="Close"> <span aria-hidden="true">&times;</span> </button>');var c="";t&&(c=' <input type="checkbox" class="delete-book-checkbox" name="" value=""> &nbsp; &nbsp;');var b='<tr class="selected_book_row '+(a?"add":"")+'" id="'+l+'"><td class="book-title-td" scope="row">'+c+i+' </td><td class="book-isbn-number-td" scope="row">'+n+' </td><td class="accession-number-td" scope="row"><select class="form-control mandatory-field" id="accession-number">'+r.map(function(e){return 0==d&&"true"==s?(d=1,'<option value="">Select Accession Number</option>'):'<option value="'+e.accessionId+'">'+e.accessionNumber+"</option>"}).join("")+'</select></td><td class="issue-book-duration-td" scope="row"><input type="text" class="form-control mandatory-field" id="issue-book-duration" placeholder="Enter issue book duration (e.g., 30)"></td><td class="book-issued-at-td" scope="row"><input type="text" class="form-control select-date book-issue-date mandatory-field" placeholder="Issue Date"/></td><td>'+u+"</td></tr>";$("#book-search-row").before(b),$(".book-issue-date").val(getTodayDateInInputFormat()),initDate(36500),$("#books-search-result").html(""),$("#books-search").val(""),issueBookDetailsMenu.deletePurchaseItemEntry()},deletePurchaseItemEntry:function(){$(".delete-book-row").click(function(){$(this).parent().parent().remove()})},issueBookDetails:function(){var e=[],o=$("#book-issue-modal-session-id").text().trim();$(".selected_book_row.add").each(function(){if(validateMandatoryFields($(this)))return showErrorDialogBox("Invalid book information. Please try again."),!1;var t=$(this).attr("id"),a=$(this).find("#accession-number").val().trim(),i=$(this).find("#issue-book-duration").val().trim(),n=getDate($(this).find(".book-issue-date").val()).getTime()/1e3,l=$("#book-issue-modal-student-id").text().trim(),r={bookId:t,accessionId:a,academicSessionId:o,issuedToUserId:l,issuedToUserType:"STUDENT",duration:i,issuedAt:n};e.push(r)}),e.length<=0?alert("Please select atleast a book to issue!"):($("#book-issue-modal").modal("toggle"),ajaxClient.post("/library-management/issue-book/"+o,JSON.stringify(e),function(o){if($("#issue-book-transaction-detail-modal-container").html(o),"True"==$("#success").text().trim()){var t=e[0];issueBookDetailsMenu.loadEntityBookAssignment(t.academicSessionId,STUDENT_ENTITY,t.issuedToUserId,STUDENT_ASSIGNED_BOOK_LIST,!0,"ISSUED")}else $("#issue-book-status-modal").modal("toggle")}))},returnBookDetails:function(e,o,t){$("#book-return-confirm-button").on("click",function(){if(0==issueBookDetailsMenu.dataCache.selectedIssuedBookCount)return $("#book-return-conformation-popup").modal("hide"),void alert("Please select book first to return");var a=0;if($("div.returnDate").find("input.book-Return-date").length>0&&(a=getDate($("div.returnDate").find("input.book-Return-date").val()).getTime()/1e3),0!=a){var i={academicSessionId:e,transactionIds:t,returnDate:a,issueStatus:"RETURNED"};$("#book-return-conformation-popup").modal("toggle"),ajaxClient.post("/library-management/return-books",JSON.stringify(i),function(t){$("#issue-book-transaction-detail-modal-container").html(t),"True"==$("#success").text().trim()?issueBookDetailsMenu.loadEntityBookAssignment(e,STUDENT_ENTITY,o,STUDENT_ASSIGNED_BOOK_LIST,!0,"RETURNED"):$("#issue-book-status-modal").modal("toggle")})}else alert("Please select Return Book Date!")})},selectStudentIssuedBookList:function(e){if(e.checked){$(".student-issued-books-select-checkbox").prop("checked",!0);var o=$("input.student-issued-books-select-checkbox:checkbox:checked").length;$("#student-issued-books-selected-count").html(o),issueBookDetailsMenu.dataCache.selectedIssuedBookCount=o}else $(".student-issued-books-select-checkbox").prop("checked",!1),$("#student-issued-books-selected-count").html(0),issueBookDetailsMenu.dataCache.selectedIssuedBookCount=0},studentIssuedBookSelectCheckbox:function(e){var o=$("input.student-issued-books-select-checkbox:checkbox:checked").length;$("#student-issued-books-selected-count").html(o),issueBookDetailsMenu.dataCache.selectedIssuedBookCount=o},getSelectedBulkTransactionIds:function(){var e=[];return $("input.student-issued-books-select-checkbox").each(function(){if($(this).is(":checked")){var o=$(this).parent().find("p.bulk-return-issued-transaction-id").first().text().trim();e.push(o)}}),e}},bookList={dataCache:{},initPagination:function(){pagination.bindEvents(function(){manageBook.searchBook(!1)},function(){manageBook.searchBook(!1)},function(){manageBook.searchBook(!1)},function(){manageBook.searchBook(!0)})},bookDetailsInitPagination:function(e,o,t,a,i){pagination.bindEvents(function(){issueBookDetailsMenu.loadEntityBookAssignment(e,o,t,a,!1,i)},function(){issueBookDetailsMenu.loadEntityBookAssignment(e,o,t,a,!1,i)},function(){issueBookDetailsMenu.loadEntityBookAssignment(e,o,t,a,!1,i)},function(){issueBookDetailsMenu.loadEntityBookAssignment(e,o,t,a,!0,i)})}},libraryTypeDetails={loadLibraryTypeDetailsHome:function(){ajaxClient.get("/library-management/library-type-view",function(e){$("#main-content").html(e),libraryTypeDetails.addLibraryTypeDetails(),libraryTypeDetails.updateLibraryTypeDetails(),libraryTypeDetails.deleteLibraryTypeDetails()})},addLibraryTypeDetails:function(){$("#add-library-type-button").on("click",function(){var e=$("#library-type-name-textarea").val();if(""!==e&&null!=e){var o={libraryName:e};ajaxClient.post("/library-management/add-library-type",{libraryTypePayload:JSON.stringify(o)},function(e){$("#library-type-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){$("#library-type-name-textarea").val(""),libraryTypeDetails.loadLibraryTypeDetailsHome()})})}else showErrorDialogBox("Please add library type name!")})},updateLibraryTypeDetails:function(){$(".update-library-type").on("click",function(){var e=$(this).parent().parent().find(".library-type-info-json").text().trim(),o=JSON.parse(e);$("#update-library-type-id").text(o.libraryTypeId),$("#library-type-name-text").val(o.libraryName),$("#update-library-type-popup-modal").modal("toggle")}),$("#update-library-type-button").on("click",function(){var e=$("#update-library-type-id").text(),o=$("#library-type-name-text").val();if(""!==o&&null!=o){var t={libraryTypeId:e,libraryName:o};$("#update-library-type-popup-modal").modal("toggle"),ajaxClient.post("/library-management/update-library-type",{libraryTypePayload:JSON.stringify(t)},function(e){$("#library-type-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){libraryTypeDetails.loadLibraryTypeDetailsHome()})})}else showErrorDialogBox("Please add library type name!")})},deleteLibraryTypeDetails:function(){$(".delete-library-type").on("click",function(){var e=$(this).parent().parent().find(".library-type-info-json").text().trim(),o=JSON.parse(e);$("#delete-library-type-id").text(o.libraryTypeId),$("#delete-library-type-popup-modal").modal("toggle")}),$("#delete-library-type-button").on("click",function(){var e=$("#delete-library-type-id").text();$("#delete-library-type-popup-modal").modal("toggle"),ajaxClient.post("/library-management/delete-library-type/"+e,{},function(e){$("#library-type-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){libraryTypeDetails.loadLibraryTypeDetailsHome()})})})}},publicationDetails={loadPublicationDetailsHome:function(){ajaxClient.get("/library-management/publication-view",function(e){$("#main-content").html(e),publicationDetails.addPublicationDetails(),publicationDetails.viewPublicationDetails(),publicationDetails.updatePublicationDetails(),publicationDetails.deletePublicationDetails()})},addPublicationDetails:function(){$("#add-publication-button").on("click",function(){var e=$("#publication-name").val();if(""!==e&&null!=e){var o=$("#publication-address").val(),t=$("#publication-phone").val(),a=$("#publication-email").val(),i=$("#publication-website").val();""!==o&&null!=o||(o=null),""!==t&&null!=t||(t=null),""!==a&&null!=a||(a=null),""!==i&&null!=i||(i=null);var n={publicationName:e,address:o,phoneNumber:t,email:a,website:i};ajaxClient.post("/library-management/add-publication",{publicationPayload:JSON.stringify(n)},function(e){$("#publication-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){$("#publication-name").val(""),$("#publication-address").val(""),$("#publication-phone").val(""),$("#publication-email").val(""),$("#publication-website").val(""),publicationDetails.loadPublicationDetailsHome()})})}else showErrorDialogBox("Please add publication name!")})},viewPublicationDetails:function(){$(".view-publication").on("click",function(){var e=$(this).parent().parent().find(".publication-info-json").text().trim(),o=JSON.parse(e);$("#view-publication-name").text(o.publicationName),$("#view-publication-address").text(o.address),$("#view-publication-phone").text(o.phoneNumber),$("#view-publication-email").text(o.email),$("#view-publication-website").text(o.website),$("#view-publication-popup-modal").modal("toggle")})},updatePublicationDetails:function(){$(".update-publication").on("click",function(){var e=$(this).parent().parent().find(".publication-info-json").text().trim(),o=JSON.parse(e);$("#update-publication-id").text(o.publicationId),$("#publication-name-text").val(o.publicationName),$("#publication-address-text").val(o.address),$("#publication-phone-text").val(o.phoneNumber),$("#publication-email-text").val(o.email),$("#publication-website-text").val(o.website),$("#update-publication-popup-modal").modal("toggle")}),$("#update-publication-button").on("click",function(){var e=$("#update-publication-id").text(),o=$("#publication-name-text").val();if(""!==o&&null!=o){var t=$("#publication-address-text").val(),a=$("#publication-phone-text").val(),i=$("#publication-email-text").val(),n=$("#publication-website-text").val();""!==t&&null!=t||(t=null),""!==a&&null!=a||(a=null),""!==i&&null!=i||(i=null),""!==n&&null!=n||(n=null);var l={publicationId:e,publicationName:o,address:t,phoneNumber:a,email:i,website:n};$("#update-publication-popup-modal").modal("toggle"),ajaxClient.post("/library-management/update-publication",{publicationPayload:JSON.stringify(l)},function(e){$("#publication-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){publicationDetails.loadPublicationDetailsHome()})})}else showErrorDialogBox("Please add publication name!")})},deletePublicationDetails:function(){$(".delete-publication").on("click",function(){var e=$(this).parent().parent().find(".publication-info-json").text().trim(),o=JSON.parse(e);$("#delete-publication-id").text(o.publicationId),$("#delete-publication-popup-modal").modal("toggle")}),$("#delete-publication-button").on("click",function(){var e=$("#delete-publication-id").text();$("#delete-publication-popup-modal").modal("toggle"),ajaxClient.post("/library-management/delete-publication/"+e,{},function(e){$("#publication-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){publicationDetails.loadPublicationDetailsHome()})})})}},publisherDetails={loadPublisherDetailsHome:function(){ajaxClient.get("/library-management/publisher-view",function(e){$("#main-content").html(e),publisherDetails.addPublisherDetails(),publisherDetails.viewPublisherDetails(),publisherDetails.updatePublisherDetails(),publisherDetails.deletePublisherDetails()})},addPublisherDetails:function(){$("#add-publisher-button").on("click",function(){var e=$("#publisher-name").val();if(""!==e&&null!=e){var o={publisherName:e,contactInformation:$("#publisher-contact").val(),affiliation:$("#publisher-affiliation").val()};ajaxClient.post("/library-management/add-publisher",{publisherPayload:JSON.stringify(o)},function(e){$("#publisher-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){$("#publisher-name").val(""),$("#publisher-contact").val(""),$("#publisher-affiliation").val(""),publisherDetails.loadPublisherDetailsHome()})})}else showErrorDialogBox("Please add publisher name!")})},viewPublisherDetails:function(){$(".view-publisher").on("click",function(){var e=$(this).parent().parent().find(".publisher-info-json").text().trim(),o=JSON.parse(e);$("#view-publisher-name").text(o.publisherName),$("#view-publisher-contact").text(o.contactInformation),$("#view-publisher-affiliation").text(o.affiliation),$("#view-publisher-popup-modal").modal("toggle")})},updatePublisherDetails:function(){$(".update-publisher").on("click",function(){var e=$(this).parent().parent().find(".publisher-info-json").text().trim(),o=JSON.parse(e);$("#update-publisher-id").text(o.publisherId),$("#publisher-name-text").val(o.publisherName),$("#publisher-contact-text").val(o.contactInformation),$("#publisher-affiliation-text").val(o.affiliation),$("#update-publisher-popup-modal").modal("toggle")}),$("#update-publisher-button").on("click",function(){var e=$("#update-publisher-id").text(),o=$("#publisher-name-text").val();if(""!==o&&null!=o){var t={publisherId:e,publisherName:o,contactInformation:$("#publisher-contact-text").val(),affiliation:$("#publisher-affiliation-text").val()};$("#update-publisher-popup-modal").modal("toggle"),ajaxClient.post("/library-management/update-publisher",{publisherPayload:JSON.stringify(t)},function(e){$("#publisher-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){publisherDetails.loadPublisherDetailsHome()})})}else showErrorDialogBox("Please add publisher name!")})},deletePublisherDetails:function(){$(".delete-publisher").on("click",function(){var e=$(this).parent().parent().find(".publisher-info-json").text().trim(),o=JSON.parse(e);$("#delete-publisher-id").text(o.publisherId),$("#delete-publisher-popup-modal").modal("toggle")}),$("#delete-publisher-button").on("click",function(){var e=$("#delete-publisher-id").text();$("#delete-publisher-popup-modal").modal("toggle"),ajaxClient.post("/library-management/delete-publisher/"+e,{},function(e){$("#publisher-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){publisherDetails.loadPublisherDetailsHome()})})})}},authorDetails={loadAuthorDetailsHome:function(){ajaxClient.get("/library-management/author-view",function(e){$("#main-content").html(e),initDate(36500),authorDetails.addAuthorDetails(),authorDetails.viewAuthorDetails(),authorDetails.updateAuthorDetails(),authorDetails.deleteAuthorDetails()})},addAuthorDetails:function(){$("#add-author-button").on("click",function(){var e=$("#author-name").val();if(""!==e&&null!=e){var o=$("#author-nationality").val(),t=$("#author-birth-date").val(),a=$("#author-genres").val(),i=$("#author-biography").val(),n={authorName:e,nationality:o,dateOfBirth:t?getDate(t).getTime()/1e3:null,associatedGeners:a,shortBiography:i};ajaxClient.post("/library-management/add-author",{authorPayload:JSON.stringify(n)},function(e){$("#author-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){$("#author-name").val(""),$("#author-nationality").val(""),$("#author-birth-year").val(""),$("#author-genres").val(""),$("#author-biography").val(""),authorDetails.loadAuthorDetailsHome()})})}else showErrorDialogBox("Please add author name!")})},viewAuthorDetails:function(){$(".view-author").on("click",function(){var e=$(this).parent().parent().find(".author-info-json").text().trim(),o=JSON.parse(e);$("#view-author-name").text(o.authorName),$("#view-author-nationality").text(o.nationality),$("#view-author-dob").text(getFormattedDate(o.dateOfBirth)),$("#view-author-genres").text(o.associatedGeners),$("#view-author-biography").text(o.shortBiography),$("#view-author-popup-modal").modal("toggle")})},updateAuthorDetails:function(){$(".update-author").on("click",function(){var e=$(this).parent().parent().find(".author-info-json").text().trim(),o=JSON.parse(e);$("#update-author-id").text(o.authorId),$("#author-name-text").val(o.authorName),$("#author-nationality-text").val(o.nationality),$("#author-birth-year-text").val(getFormattedDate(o.dateOfBirth)),$("#author-genres-text").val(o.associatedGeners),$("#author-biography-text").val(o.shortBiography),$("#update-author-popup-modal").modal("toggle")}),$("#update-author-button").on("click",function(){var e=$("#update-author-id").text(),o=$("#author-name-text").val();if(""!==o&&null!=o){var t=$("#author-nationality-text").val(),a=$("#author-birth-year-text").val(),i=$("#author-genres-text").val(),n=$("#author-biography-text").val();""!==a&&null!=a||(a=null),""!==i&&null!=i||(i=null),""!==n&&null!=n||(n=null),""!==t&&null!=t||(t=null);var l={authorId:e,authorName:o,nationality:t,dateOfBirth:a?getDate(a).getTime()/1e3:null,associatedGeners:i,shortBiography:n};$("#update-author-popup-modal").modal("toggle"),ajaxClient.post("/library-management/update-author",{authorPayload:JSON.stringify(l)},function(e){$("#author-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){authorDetails.loadAuthorDetailsHome()})})}else showErrorDialogBox("Please add author name!")})},deleteAuthorDetails:function(){$(".delete-author").on("click",function(){var e=$(this).parent().parent().find(".author-info-json").text().trim(),o=JSON.parse(e);$("#delete-author-id").text(o.authorId),$("#delete-author-popup-modal").modal("toggle")}),$("#delete-author-button").on("click",function(){var e=$("#delete-author-id").text();$("#delete-author-popup-modal").modal("toggle"),ajaxClient.post("/library-management/delete-author/"+e,{},function(e){$("#author-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){authorDetails.loadAuthorDetailsHome()})})})}},vendorDetails={loadVendorDetailsHome:function(){ajaxClient.get("/library-management/vendor-view",function(e){$("#main-content").html(e),vendorDetails.addVendorDetails(),vendorDetails.viewVendorDetails(),vendorDetails.updateVendorDetails(),vendorDetails.deleteVendorDetails()})},addVendorDetails:function(){$("#add-vendor-button").on("click",function(){var e=$("#vendor-name").val();if(""!==e&&null!=e){var o={vendorName:e,address:$("#vendor-address").val(),phoneNumber:$("#vendor-phone").val(),email:$("#vendor-email").val(),gstNumber:$("#vendor-gst").val()};ajaxClient.post("/library-management/add-vendor",{vendorPayload:JSON.stringify(o)},function(e){$("#vendor-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){$("#vendor-name").val(""),$("#vendor-address").val(""),$("#vendor-phone").val(""),$("#vendor-email").val(""),$("#vendor-gst").val(""),vendorDetails.loadVendorDetailsHome()})})}else showErrorDialogBox("Please add vendor name!")})},updateVendorDetails:function(){$(".update-vendor").on("click",function(){var e=$(this).parent().parent().find(".vendor-info-json").text().trim(),o=JSON.parse(e);$("#update-vendor-id").text(o.vendorId),$("#vendor-name-text").val(o.vendorName),$("#vendor-address-text").val(o.address),$("#vendor-phone-text").val(o.phoneNumber),$("#vendor-email-text").val(o.email),$("#vendor-gst-text").val(o.gstNumber),$("#vendor-account-type-text").val(o.accountType),$("#vendor-account-holder-text").val(o.accountHolderName),$("#vendor-account-number-text").val(o.accountNumber),$("#vendor-ifsc-text").val(o.ifscCode),$("#vendor-bank-name-text").val(o.bankName),$("#update-vendor-popup-modal").modal("toggle")}),$("#update-vendor-button").on("click",function(){var e=$("#update-vendor-id").text(),o=$("#vendor-name-text").val();if(""!==o&&null!=o){var t=$("#vendor-address-text").val(),a=$("#vendor-phone-text").val(),i=$("#vendor-email-text").val(),n=$("#vendor-gst-text").val(),l=$("#vendor-account-type-text").val(),r=$("#vendor-account-holder-text").val(),s=$("#vendor-account-number-text").val(),d=$("#vendor-ifsc-text").val(),u=$("#vendor-bank-name-text").val();""!==l&&null!=l||(l=null),""!==r&&null!=r||(r=null),""!==s&&null!=s||(s=null),""!==d&&null!=d||(d=null),""!==u&&null!=u||(u=null),""!==t&&null!=t||(t=null),""!==a&&null!=a||(a=null),""!==i&&null!=i||(i=null),""!==n&&null!=n||(n=null);var c={vendorId:e,vendorName:o,address:t,phoneNumber:a,email:i,gstNumber:n,accountType:l,accountHolderName:r,accountNumber:s,ifscCode:d,bankName:u};$("#update-vendor-popup-modal").modal("toggle"),ajaxClient.post("/library-management/update-vendor",{vendorPayload:JSON.stringify(c)},function(e){$("#vendor-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){vendorDetails.loadVendorDetailsHome()})})}else showErrorDialogBox("Please add vendor name!")})},deleteVendorDetails:function(){$(".delete-vendor").on("click",function(){var e=$(this).parent().parent().find(".vendor-info-json").text().trim(),o=JSON.parse(e);$("#delete-vendor-id").text(o.vendorId),$("#delete-vendor-popup-modal").modal("toggle")}),$("#delete-vendor-button").on("click",function(){var e=$("#delete-vendor-id").text();$("#delete-vendor-popup-modal").modal("toggle"),ajaxClient.post("/library-management/delete-vendor/"+e,{},function(e){$("#vendor-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){vendorDetails.loadVendorDetailsHome()})})})},viewVendorDetails:function(){$(".view-vendor").on("click",function(){var e=$(this).parent().parent().find(".vendor-info-json").text().trim(),o=JSON.parse(e);$("#view-vendor-name").text(o.vendorName),$("#view-vendor-address").text(o.address),$("#view-vendor-phone").text(o.phoneNumber),$("#view-vendor-email").text(o.email),$("#view-vendor-gst").text(o.gstNumber),$("#view-vendor-account-type").text(o.accountType),$("#view-vendor-account-holder").text(o.accountHolderName),$("#view-vendor-account-number").text(o.accountNumber),$("#view-vendor-ifsc").text(o.ifscCode),$("#view-vendor-bank-name").text(o.bankName),$("#view-vendor-popup-modal").modal("toggle")})}},genreDetails={loadGenreDetailsHome:function(){ajaxClient.get("/library-management/genre-view",function(e){$("#main-content").html(e),genreDetails.addGenreDetails(),genreDetails.updateGenreDetails(),genreDetails.deleteGenreDetails(),genreDetails.viewGenreDetails()})},addGenreDetails:function(){$("#add-genre-button").on("click",function(){var e=$("#genre-name").val();if(""!==e&&null!=e){var o={entityName:"INSTITUTE",genreName:e,classificationNumber:$("#classification-number").val()};ajaxClient.post("/library-management/add-genre",{genrePayload:JSON.stringify(o)},function(e){$("#genre-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){$("#genre-name").val(""),$("#classification-number").val(""),genreDetails.loadGenreDetailsHome()})})}else showErrorDialogBox("Please add genre name!")})},updateGenreDetails:function(){$(".update-genre").on("click",function(){var e=$(this).parent().parent().find(".genre-info-json").text().trim(),o=JSON.parse(e);$("#update-genre-id").text(o.genreId),$("#genre-name-text").val(o.genreName),$("#classification-number-text").val(o.classificationNumber),$("#update-genre-popup-modal").modal("toggle")}),$("#update-genre-button").on("click",function(){var e=$("#update-genre-id").text(),o=$("#genre-name-text").val();if(""!==o&&null!=o){var t={entityName:"INSTITUTE",genreId:e,genreName:o,classificationNumber:$("#classification-number-text").val()};$("#update-genre-popup-modal").modal("toggle"),ajaxClient.post("/library-management/update-genre",{genrePayload:JSON.stringify(t)},function(e){$("#genre-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){genreDetails.loadGenreDetailsHome()})})}else showErrorDialogBox("Please add genre name!")})},deleteGenreDetails:function(){$(".delete-genre").on("click",function(){var e=$(this).parent().parent().find(".genre-info-json").text().trim(),o=JSON.parse(e);$("#delete-genre-id").text(o.genreId),$("#delete-genre-popup").modal("toggle")}),$("#delete-genre-button").on("click",function(){var e=$("#delete-genre-id").text();$("#delete-genre-popup").modal("toggle"),ajaxClient.post("/library-management/delete-genre/"+e,{},function(e){$("#genre-status-modal-container").html(e),$("#configure-status-modal").modal({backdrop:"static",keyboard:!1}),$("#configure-status-modal").off("hidden.bs.modal").on("hidden.bs.modal",function(){genreDetails.loadGenreDetailsHome()})})})},viewGenreDetails:function(){$(".view-genre").on("click",function(){var e=$(this).parent().parent().find(".genre-info-json").text().trim(),o=JSON.parse(e);$("#view-genre-name").text(o.genreName),$("#view-classification-number").text(o.classificationNumber),$("#view-entity-name").text(o.entityName),$("#view-genre-popup-modal").modal("toggle")})}};function bookDocumentWarningPopup(){$("#book-document-warning-popup").modal("toggle")}function studentLiveSearchEvent(e,o,t,a,i){ajaxClient.get("/library-management/student-live-search/"+e+"?searchText="+o+"&status="+i,function(e){$(t).html(e),studentLiveSearchHandler.bindStudentSearchClickEvent(t,a)})}