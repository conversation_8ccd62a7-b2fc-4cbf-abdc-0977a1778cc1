from django.contrib import admin
from django.urls import path
from django.urls import include
from library_management import views
from library_management.views import *

urlpatterns = [

    # Library Management Home Page
    path('dashboard', dashboard_view, name='dashboard_view'),
    path('home', home_page_view, name='home_page_view'),
    path('session-home/<academic_session_id>', home_page_session_view, name='home_page_session_view'),
    path('book-view', add_book_details_view, name='book_details_view'),
    path('add-new-book', add_new_book_details_view, name='new_book_details_view'),
    path('update-book-View', update_book_details_view, name='book_details_view'),
    path('update-book', update_book_details, name='update_book_details_view'),
    path('update-number-of-book', update_number_of_book_details, name="update_number_of_book_details"),
    path('book-details/<offset>/<limit>', get_book_details_view, name=" get_book_details"),
    path('delete-book/<book_id>', delete_book_details_view , name="delete_book_details"),
    path('document-download/<document_id>/<book_id>', document_download_view, name='document_download_view'),
    path('document-delete/<document_id>/<book_id>', document_delete_view, name='document_delete_view'),
    path('document-upload/<book_id>', document_upload_view, name='document_upload_view'),
    path('student-live-search/<academic_session_id>', student_live_search_view, name='student_live_search_view'),
    path('issued-book', book_assignment_view, name='issued-book-view'),
    path('student-book-details/<session_id>/<entity>/<student_id>', get_book_details_assigned_view, name='get_book_details_assigned_view'),
    path('book-live-search', get_live_book_search_details_view, name='book_live_search_view'),
    path('issue-book/<academic_session_id>', issue_book_details , name="issue-book-detail-view"),
    path('return-books', return_book_details , name="return-book-detail-view"),
    path('delete-issued-Book-data/<academic_session_id>/<transaction_id>', delete_issued_book_details_view , name="delete_issued_book_details"),
    path('update-issued-book-date/<academic_session_id>/<transaction_id>', update_issued_book_details_view , name="update_issued_book_details"),
    path('library-type-view', get_library_configuration_view, name='get_library_configuration_view'),
    path('publication-view', get_publication_details_view, name='get_publication_details_view'),
    path('publisher-view', get_publisher_details_view, name='get_publisher_details_view'),
    path('author-view', get_author_details_view, name='get_author_details_view'),
    path('vendor-view', get_vendor_details_view, name='get_vendor_details_view'),
    path('genre-view', get_genre_details_view, name='get_genre_details_view'),
    path('add-publication', add_publication_details_view, name='add_publication_details_view'),
    path('update-publication', update_publication_details_view, name='update_publication_details_view'),
    path('delete-publication/<publication_id>', delete_publication_details_view, name='delete_publication_details_view'),
    path('add-publisher', add_publisher_details_view, name='add_publisher_details_view'),
    path('update-publisher', update_publisher_details_view, name='update_publisher_details_view'),
    path('delete-publisher/<publisher_id>', delete_publisher_details_view, name='delete_publisher_details_view'),
    path('add-author', add_author_details_view, name='add_author_details_view'),    
    path('update-author', update_author_details_view, name='update_author_details_view'),
    path('delete-author/<author_id>', delete_author_details_view, name='delete_author_details_view'),
    path('add-vendor', add_vendor_details_view, name='add_vendor_details_view'),
    path('update-vendor', update_vendor_details_view, name='update_vendor_details_view'),
    path('delete-vendor/<vendor_id>', delete_vendor_details_view, name='delete_vendor_details_view'),
    path('add-genre', add_genre_details_view, name='add_genre_details_view'),
    path('update-genre', update_genre_details_view, name='update_genre_details_view'),
    path('delete-genre/<genre_id>', delete_genre_details_view, name='delete_genre_details_view'),
    path('add-library-type', add_library_type_details_view, name='add_library_type_details_view'),
    path('update-library-type', update_library_type_details_view, name='update_library_type_details_view'),
    path('delete-library-type/<library_type_id>', delete_library_type_details_view, name='delete_library_type_details_view'),
]