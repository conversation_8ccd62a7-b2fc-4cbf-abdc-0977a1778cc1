import json
from core.controller.utils.restclient import *
from core.controller.user.institute import *

LIBRARY_PREFERENCES_CACHE_NAMESPACE = "library_preferences"

def add_new_book(user_login_view, institute_unique_code, add_book_details, document_name, document):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    add_book_details['bookDetailPayload']['instituteId'] = institute_id
    if document is None :
        response = restclient.upload_file(user_login_view, "/2.0/library/save-book-details/"+str(institute_id)+"?user_id="+user['uuid'], {'add_book_details' : json.dumps(add_book_details)})
    else:
        response = restclient.upload_file(user_login_view, "/2.0/library/save-book-details/"+str(institute_id)+"?user_id="+user['uuid'], {'add_book_details' : json.dumps(add_book_details), 'file' : document, 'documentName' : document_name})
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Book Added Successfully!!!"}

def update_book(user_login_view, institute_unique_code, update_book_details):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    update_book_details['bookDetailPayload']['instituteId'] = institute_id

    response = restclient.put(user_login_view, "/2.0/library/book-details-modernize/"+str(institute_id) +"?user_id="+user['uuid'], update_book_details)

    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Book Added Successfully!!!"}

def update_number_of_books(user_login_view, institute_unique_code, update_copies_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    
    response = restclient.put(user_login_view, "/2.0/library/number-of-book-details-modernize/"+str(institute_id) +"?user_id="+user['uuid'], update_copies_payload)

    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Number Of Copies Updated Successfully!!!"}

def issue_book(user_login_view, institute_unique_code, academic_session_id, issue_book_details):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    for issue_book in issue_book_details:
        issue_book['instituteId'] = int(institute_id)
        issue_book['issuedBy'] = user['uuid']

    response = restclient.post(user_login_view, "/2.0/library/issue-book/"+str(institute_id)+"?user_id="+user['uuid']+"&academic_session_id="+academic_session_id, issue_book_details)

    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Book issued Successfully!!!"}

def return_book(user_login_view, institute_unique_code, return_book_details):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return_book_details['instituteId'] = int(institute_id)
    return_book_details['receivedBy'] = user['uuid']
        
    response = restclient.put(user_login_view, "/2.0/library/return-books?institute_id="+str(institute_id)+"&user_id="+user['uuid'], return_book_details)

    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Book Returned Successfully!!!"}

def get_library_preferences(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    cached_examination_preferences = cache_provider.get_value(LIBRARY_PREFERENCES_CACHE_NAMESPACE, institute_id)
    if cached_examination_preferences is None:
        response = restclient.get(user_login_view, "/2.0/user-preferences/library?institute_id="+str(institute_id)).get_data()
        cache_provider.set_value(LIBRARY_PREFERENCES_CACHE_NAMESPACE, institute_id, response)
        return response
    return cached_examination_preferences

def get_library_details(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    library_details = restclient.get(user_login_view, "/2.0/library/library-details/"+str(institute_id)).get_data()
    return library_details

def get_publication_details(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    publication_list = restclient.get(user_login_view, "/2.0/library/publication-details/"+str(institute_id)).get_data()
    return publication_list

def get_publisher_details(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    publisher_list = restclient.get(user_login_view, "/2.0/library/publisher-details/"+str(institute_id)).get_data()
    return publisher_list

def get_genre_details(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    genre_list = restclient.get(user_login_view, "/2.0/library/genre-details/"+str(institute_id)).get_data()
    return genre_list

def get_vendor_details(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    vendor_list = restclient.get(user_login_view, "/2.0/library/vendor-details/"+str(institute_id)).get_data()
    return vendor_list

def get_author_details(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    author_list = restclient.get(user_login_view, "/2.0/library/author-details/"+str(institute_id)).get_data()
    return author_list

def get_library_type_details(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    library_type_list = restclient.get(user_login_view, "/2.0/library/library-type-details/"+str(institute_id)).get_data()
    return library_type_list

def get_book_details(user_login_view, institute_unique_code, text, offSet = None, limit = None):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    if offSet is None or limit is None:
        book_list_with_pagination_info = restclient.get(user_login_view, "/2.0/library/book-details/"+str(institute_id)+"?search_text="+text).get_data()
    else:
        book_list_with_pagination_info = restclient.get(user_login_view, "/2.0/library/book-details/"+str(institute_id)+"?search_text="+text+"&limit="+str(limit)+"&offset="+str(offSet)).get_data()
    return book_list_with_pagination_info

def get_search_book_details(user_login_view, institute_unique_code, text):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    book_list = restclient.get(user_login_view, "/2.0/library/book-details-search/"+str(institute_id)+"?search_text="+text).get_data()
    return book_list

def delete_book_details(user_login_view, institute_unique_code, bookId):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/library/book-details-excise/"+bookId+"/"+str(institute_id)+"?user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Book Deleted Successfully!!!"}

def delete_issued_book_details(user_login_view, institute_unique_code, transactionId, academic_session_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/library/issued-book/"+transactionId+"/"+str(institute_id)+"?user_id="+user['uuid']+"&academic_session_id="+ str(academic_session_id))
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Issued Book Deleted Successfully!!!"}

def update_issued_book_details(user_login_view, institute_unique_code, transactionId, academic_session_id, status, updateBookDate):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/library/issued-book/"+transactionId+"?user_id="+user['uuid']+"&institute_id="+str(institute_id)+"&academic_session_id="+ str(academic_session_id)+"&status="+status+"&update-book-date="+str(updateBookDate),{})
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Issued Book Date Update Successfully!!!"}

def download_document(user_login_view, institute_unique_code, document_id, book_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/library/download-document/"+book_id+"/"+document_id+ "?institute_id=" + str(institute_id))

def delete_document(user_login_view, institute_unique_code, document_id, book_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.post(user_login_view, "/2.0/library/delete-document/"+book_id+"/"+document_id+ "?user_id="+str(user['uuid'])+"&institute_id="+str(institute_id), {})

def upload_document(user_login_view, institute_unique_code, document_type, document_name, document, book_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.upload_file(user_login_view, "/2.0/library/upload-document/"+book_id+"?institute_id="+str(institute_id) + "&user_id="+user['uuid'],{'file' : document, 'documentType' : document_type, 'documentName' : document_name})

def get_user_book_details(user_login_view, institute_unique_code, sessionId, studentId, status, offSet = None, limit = None):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    if offSet is None or limit is None:
        student_book_list_with_pagination_info = restclient.get(user_login_view, "/2.0/library/issued-books?institute_id="+str(institute_id)+"&academic_session_id="+str(sessionId)+"&status="+status+"&issued_to="+studentId).get_data()
    else:
        student_book_list_with_pagination_info = restclient.get(user_login_view, "/2.0/library/issued-books?institute_id="+str(institute_id)+"&academic_session_id="+str(sessionId)+"&status="+status+"&issued_to="+studentId+"&limit="+str(limit)+"&offset="+str(offSet)).get_data()
    return student_book_list_with_pagination_info

def add_publisher_details(user_login_view, institute_unique_code, publisher_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/library/save-publisher-details/"+str(institute_id)+"?user_id="+user['uuid'], publisher_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Publisher Added Successfully!!!"}

def add_publication_details(user_login_view, institute_unique_code, publication_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/library/save-publication-details/"+str(institute_id)+"?user_id="+user['uuid'], publication_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Publication Added Successfully!!!"}

def add_genre_details(user_login_view, institute_unique_code, genre_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/library/save-genre-details/"+str(institute_id)+"?user_id="+user['uuid'], genre_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Genre Added Successfully!!!"}

def add_vendor_details(user_login_view, institute_unique_code, vendor_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/library/save-vendor-details/"+str(institute_id)+"?user_id="+user['uuid'], vendor_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Vendor Added Successfully!!!"}

def add_author_details(user_login_view, institute_unique_code, author_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/library/save-author-details/"+str(institute_id)+"?user_id="+user['uuid'], author_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Author Added Successfully!!!"}

def update_publication_details(user_login_view, institute_unique_code, publication_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/library/update-publication-details/"+str(institute_id)+"?user_id="+user['uuid'], publication_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Publication Updated Successfully!!!"}

def update_publisher_details(user_login_view, institute_unique_code, publisher_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/library/update-publisher-details/"+str(institute_id)+"?user_id="+user['uuid'], publisher_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Publisher Updated Successfully!!!"}

def update_genre_details(user_login_view, institute_unique_code, genre_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/library/update-genre-details/"+str(institute_id)+"?user_id="+user['uuid'], genre_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Genre Updated Successfully!!!"}

def update_vendor_details(user_login_view, institute_unique_code, vendor_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/library/update-vendor-details/"+str(institute_id)+"?user_id="+user['uuid'], vendor_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Vendor Updated Successfully!!!"}

def update_author_details(user_login_view, institute_unique_code, author_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/library/update-author-details/"+str(institute_id)+"?user_id="+user['uuid'], author_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Author Updated Successfully!!!"}

def update_library_type_details(user_login_view, institute_unique_code, library_type_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.put(user_login_view, "/2.0/library/update-library-type-details/"+str(institute_id)+"?user_id="+user['uuid'], library_type_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Library Type Updated Successfully!!!"}

def delete_publication_details(user_login_view, institute_unique_code, publication_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/library/publication-details/"+publication_id+"/"+str(institute_id)+"?user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Publication Deleted Successfully!!!"}

def delete_publisher_details(user_login_view, institute_unique_code, publisher_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/library/publisher-details/"+publisher_id+"/"+str(institute_id)+"?user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Publisher Deleted Successfully!!!"}

def delete_genre_details(user_login_view, institute_unique_code, genre_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/library/genre-details/"+genre_id+"/"+str(institute_id)+"?user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Genre Deleted Successfully!!!"}

def delete_vendor_details(user_login_view, institute_unique_code, vendor_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/library/vendor-details/"+vendor_id+"/"+str(institute_id)+"?user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Vendor Deleted Successfully!!!"}

def delete_author_details(user_login_view, institute_unique_code, author_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/library/author-details/"+author_id+"/"+str(institute_id)+"?user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Author Deleted Successfully!!!"}

def delete_library_type_details(user_login_view, institute_unique_code, library_type_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/library/library-type-details/"+library_type_id+"/"+str(institute_id)+"?user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Library Type Deleted Successfully!!!"}

def add_library_type_details(user_login_view, institute_unique_code, library_type_payload):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/library/save-library-type-details/"+str(institute_id)+"?user_id="+user['uuid'], library_type_payload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Library Type Added Successfully!!!"}
