{% load json %}
<p style="display:none;">{{authorDetails|jsonstr}}</p>
<div class="card">
    <div class="card-body">
        <div class="col-8" style="display:inline-block">
            <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
            <caption>List of Authors</caption>
            <thead>
                <tr>
                <th scope="col">#</th>
                <th scope="col">Author Name</th>
                <th scope="col" colspan="3">Action</th>
                </tr>
            </thead>
            <tbody>
                {% for authorDetail in authorDetails %}
                    <tr>
                    <td style="display:none;">
                        <p class="author-info-json" style="display:none;"> {{authorDetail|jsonstr}} </p>
                    </td>
                    <th scope="row">{{ forloop.counter }}</th>
                    <td>{{authorDetail.authorName}}</td>
                    <td style="width:10%;" >
                        <button type="button" class="btn btn-outline-info view-author">
                        View
                        </button>
                    </td>
                    <td style="width:10%;">
                        <button type="button" class="btn btn-outline-warning update-author">
                        Update
                        </button>
                    </td>
                    <td style="width:10%;">
                        <button type="button" class="btn btn-outline-danger delete-author">
                            Delete
                        </button>
                    </td>
                    </tr>
                {% endfor %}
            </tbody>
            </table>
        </div>
        <div class="col-4" style="display:inline-block;padding:0%;float:right;">
        <div class="col text-center">
            <div class="card" style="border: 2px solid #dee2e6; border-radius: 8px; padding: 20px; background-color: #f8f9fa;">
                <div class="card-header" style="background-color: #e9ecef; border-bottom: 1px solid #dee2e6; margin: -20px -20px 20px -20px; padding: 15px 20px; border-radius: 6px 6px 0 0;">
                    <h5 style="margin: 0; color: #495057; font-weight: bold;">Add Author</h5>
                </div>
                <form>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-name" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Author Name<span style="color: red;">*</span></label>
                        <input type="text" class="form-control" id="author-name" placeholder="Author Name" required style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-nationality" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Nationality</label>
                        <input type="text" class="form-control" id="author-nationality" placeholder="Nationality" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-birth-year" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Date of Birth</label>
                        <input type="text" class="form-control select-date" id="author-birth-date" placeholder="Birth Date" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-genres" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Associated Genres</label>
                        <input type="text" class="form-control" id="author-genres" placeholder="Associated Genres" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-biography" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Short Biography</label>
                        <textarea class="form-control" id="author-biography" rows="3" placeholder="Short Biography" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px; resize: vertical;"></textarea>
                    </div>
                </form>
                <button id="add-author-button" type="button" class="btn btn-outline-primary" style="margin-top: 15px; padding: 10px 30px; font-weight: bold; border-radius: 5px;">Add Author</button>
            </div>
        </div>
        </div>
    </div>
    <div id="author-status-modal-container"></div>
</div>

<div id="view-author-popup-modal" class="modal fade bd-example-modal-xs" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xs">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">View Author Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group col-md-12">
                        <label>Author Name:</label>
                        <p id="view-author-name"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Nationality:</label>
                        <p id="view-author-nationality"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Date of Birth:</label>
                        <p id="view-author-dob"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Associated Genres:</label>
                        <p id="view-author-genres"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Short Biography:</label>
                        <p id="view-author-biography"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="update-author-popup-modal" class="modal fade bd-example-modal-xs" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xs">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> Update Author Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                  <div class="form-row">
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                      <p style="display:none" id = "update-author-id"></p>
                      <label for="author-name-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Author Name<span style="color: red;">*</span></label>
                        <input type="text" id="author-name-text" class="form-control mandatory-field" placeholder="Author Name..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-nationality-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Nationality</label>
                        <input type="text" id="author-nationality-text" class="form-control" placeholder="Nationality..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-birth-year-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Date of Birth</label>
                        <input type="text" id="author-birth-year-text" class="form-control select-date" placeholder="Birth Year..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-genres-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Associated Genres</label>
                        <input type="text" id="author-genres-text" class="form-control" placeholder="Associated Genres..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="author-biography-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Short Biography</label>
                        <textarea id="author-biography-text" class="form-control" rows="3" placeholder="Short Biography..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px; resize: vertical;"></textarea>
                    </div>
                  </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" id="update-author-button">Update</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-author-popup-modal" class="modal fade" tabindex="-1" role="dialog">
 <div class="modal-dialog" role="document">
   <div class="modal-content">
     <div class="modal-header">
       <h5 class="modal-title">Delete Author</h5>
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
       <p style="color:red;"> <strong> Are you sure to Delete Author? </strong></p>
     </div>
     <div class="modal-footer">
       <p id="delete-author-id" style="display:none;"></p>
       <button type="button" class="btn btn-danger" id="delete-author-button">Yes, Delete</button>
       <button type="button" class="btn btn-success" data-dismiss="modal">Review</button>
     </div>
   </div>
 </div>
</div>