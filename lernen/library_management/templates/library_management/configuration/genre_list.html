{% load json %}
<p style="display:none;">{{genreDetails|jsonstr}}</p>
<div class="card">
    <div class="card-body">
        <div class="col-8" style="display:inline-block">
            <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
            <caption>List of Genres</caption>
            <thead>
                <tr>
                <th scope="col">#</th>
                <th scope="col">Genre Name</th>
                <th scope="col">Entity Name</th>
                <th scope="col" colspan="3">Action</th>
                </tr>
            </thead>
            <tbody>
                {% for genreDetail in genreDetails %}
                    <tr>
                    <td style="display:none;">
                        <p class="genre-info-json" style="display:none;"> {{genreDetail|jsonstr}} </p>
                    </td>
                    <th scope="row">{{ forloop.counter }}</th>
                    <td>{{genreDetail.genreName}}</td>
                    <td>{{genreDetail.entityName}}</td>
                    <td style="width:10%;" >
                        <button type="button" class="btn btn-outline-info view-genre" data-target="#view-genre-popup" data-keyboard="false" data-toggle="modal" data-backdrop="static" onclick="genreConfiguration.viewGenrePopup(this);">
                        View
                        </button>
                    </td>
                    {% if genreDetail.entityName != "GLOBAL" %}
                    <td style="width:10%;" >
                        <button type="button" class="btn btn-outline-warning update-genre">
                        Update
                        </button>
                    </td>
                    <td style="width:10%;">
                        <button type="button" class="btn btn-outline-danger delete-genre">
                            Delete
                        </button>
                    </td>
                    {% else %}
                    <td style="width:10%;"></td>
                    <td style="width:10%;"></td>
                    {% endif %}
                    </tr>
                {% endfor %}
            </tbody>
            </table>
        </div>
        <div class="col-4" style="display:inline-block;padding:0%;float:right;">
        <div class="col text-center">
            <div class="card" style="border: 2px solid #dee2e6; border-radius: 8px; padding: 20px; background-color: #f8f9fa;">
                <div class="card-header" style="background-color: #e9ecef; border-bottom: 1px solid #dee2e6; margin: -20px -20px 20px -20px; padding: 15px 20px; border-radius: 6px 6px 0 0;">
                    <h5 style="margin: 0; color: #495057; font-weight: bold;">Add Genre</h5>
                </div>
                <form>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="genre-name" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Genre Name<span style="color: red;">*</span></label>
                        <input type="text" class="form-control" id="genre-name" placeholder="Genre Name" required style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="classification-number" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Classification Number</label>
                        <input type="text" class="form-control" id="classification-number" placeholder="Classification Number" required style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                </form>
                <button id="add-genre-button" type="button" class="btn btn-outline-primary" style="margin-top: 15px; padding: 10px 30px; font-weight: bold; border-radius: 5px;">Add Genre</button>
            </div>
        </div>
        </div>
    </div>
    <div id="genre-status-modal-container"></div>
</div>

<div id="view-genre-popup" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">View Genre Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label>Genre Name:</label>
                        <p id="view-genre-name"></p>
                    </div>
                    <div class="form-group col-md-6">
                        <label>Classification Number:</label>
                        <p id="view-classification-number"></p>
                    </div>
                    <div class="form-group col-md-6">
                        <label>Entity Name:</label>
                        <p id="view-entity-name"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="update-genre-popup-modal" class="modal fade bd-example-modal-md" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Genre Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                  <div class="form-row">
                    <p style="display:none" id="update-genre-id"></p>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="genre-name-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Genre Name<span style="color: red;">*</span></label>
                        <input type="text" id="genre-name-text" class="form-control mandatory-field" placeholder="Genre Name..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="classification-number-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Classification Number</label>
                        <input type="text" id="classification-number-text" class="form-control mandatory-field" placeholder="Classification Number..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                  </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" id="update-genre-button">Update</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-genre-popup" class="modal fade" tabindex="-1" role="dialog">
 <div class="modal-dialog" role="document">
   <div class="modal-content">
     <div class="modal-header">
       <h5 class="modal-title">Delete Genre</h5>
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
       <p style="color:red;"> <strong> Are you sure to Delete Genre? </strong></p>
     </div>
     <div class="modal-footer">
       <p id="delete-genre-id" style="display:none;"></p>
       <button type="button" class="btn btn-danger" id="delete-genre-button">Yes, Delete</button>
       <button type="button" class="btn btn-success" data-dismiss="modal">Review</button>
     </div>
   </div>
 </div>
</div>
