{% load json %}
<p style="display:none;">{{publicationDetails|jsonstr}}</p>
<div class="card">
    <div class="card-body">
        <div class="col-8" style="display:inline-block">
            <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
            <caption>List of Publications</caption>
            <thead>
                <tr>
                <th scope="col">#</th>
                <th scope="col">Publication Name</th>
                <th scope="col" colspan="3">Action</th>
                </tr>
            </thead>
            <tbody>
                {% for publicationDetail in publicationDetails %}
                    <tr>
                    <td style="display:none;">
                        <p class="publication-info-json" style="display:none;"> {{publicationDetail|jsonstr}} </p>
                    </td>
                    <th scope="row">{{ forloop.counter }}</th>
                    <td>{{publicationDetail.publicationName}}</td>
                    <td style="width:10%;" >
                        <button type="button" class="btn btn-outline-info view-publication">
                        View
                        </button>
                    </td>
                    <td style="width:10%;" >
                        <button type="button" class="btn btn-outline-warning update-publication">
                        Update
                        </button>
                    </td>
                    <td style="width:10%;">
                        <button type="button" class="btn btn-outline-danger delete-publication">
                            Delete
                        </button>
                    </td>
                    </tr>
                {% endfor %}
            </tbody>
            </table>
        </div>
        <div class="col-4" style="display:inline-block;padding:0%;float:right;">
        <div class="col text-center">
            <div class="card" style="border: 2px solid #dee2e6; border-radius: 8px; padding: 20px; background-color: #f8f9fa;">
                <div class="card-header" style="background-color: #e9ecef; border-bottom: 1px solid #dee2e6; margin: -20px -20px 20px -20px; padding: 15px 20px; border-radius: 6px 6px 0 0;">
                    <h5 style="margin: 0; color: #495057; font-weight: bold;">Add Publication</h5>
                </div>
                <form>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-name" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Publication Name<span style="color: red;">*</span></label>
                        <input type="text" class="form-control" id="publication-name" placeholder="Publication Name" required style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-address" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Address</label>
                        <textarea class="form-control" id="publication-address" rows="2" placeholder="Address" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px; resize: vertical;"></textarea>
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-phone" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Phone Number</label>
                        <input type="number" class="form-control" id="publication-phone" placeholder="Phone Number" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-email" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Email</label>
                        <input type="email" class="form-control" id="publication-email" placeholder="Email" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-website" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Website</label>
                        <input type="url" class="form-control" id="publication-website" placeholder="Website" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                </form>
                <button id="add-publication-button" type="button" class="btn btn-outline-primary" style="margin-top: 15px; padding: 10px 30px; font-weight: bold; border-radius: 5px;">Add Publication</button>
            </div>
        </div>
        </div>
    </div>
    <div id="publication-status-modal-container"></div>
</div>

<div id="view-publication-popup-modal" class="modal fade bd-example-modal-xs" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xs">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">View Publication Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group col-md-12">
                        <label>Publication Name:</label>
                        <p id="view-publication-name"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Address:</label>
                        <p id="view-publication-address"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Phone Number:</label>
                        <p id="view-publication-phone"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Email:</label>
                        <p id="view-publication-email"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Website:</label>
                        <p id="view-publication-website"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="update-publication-popup-modal" class="modal fade bd-example-modal-xs" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xs">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> Update Publication Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                  <div class="form-row">
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                      <p style="display:none" id = "update-publication-id"></p>
                      <label for="publication-name-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Publication Name<span style="color: red;">*</span></label>
                        <input type="text" id="publication-name-text" class="form-control mandatory-field" placeholder="Publication Name..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-address-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Address</label>
                        <textarea id="publication-address-text" class="form-control" rows="2" placeholder="Address..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px; resize: vertical;"></textarea>
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-phone-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Phone Number</label>
                        <input type="number" id="publication-phone-text" class="form-control" placeholder="Phone Number..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-email-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Email</label>
                        <input type="email" id="publication-email-text" class="form-control" placeholder="Email..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="publication-website-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Website</label>
                        <input type="url" id="publication-website-text" class="form-control" placeholder="Website..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                  </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" id="update-publication-button">Update</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-publication-popup-modal" class="modal fade" tabindex="-1" role="dialog">
 <div class="modal-dialog" role="document">
   <div class="modal-content">
     <div class="modal-header">
       <h5 class="modal-title">Delete Publication</h5>
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
       <p style="color:red;"> <strong> Are you sure to Delete Publication? </strong></p>
     </div>
     <div class="modal-footer">
       <p id="delete-publication-id" style="display:none;"></p>
       <button type="button" class="btn btn-danger" id="delete-publication-button">Yes, Delete</button>
       <button type="button" class="btn btn-success" data-dismiss="modal">Review</button>
     </div>
   </div>
 </div>
</div>
