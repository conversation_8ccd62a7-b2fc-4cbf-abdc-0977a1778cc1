{% load json %}
<p style="display:none;">{{libraryDetails|jsonstr}}</p>
<div class="card">
    <div class="card-body">
        <div class="col-8" style="display:inline-block">
            <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
            <caption>List of Library Types</caption>
            <thead>
                <tr>
                <th scope="col">#</th>
                <th scope="col">Library Type Name</th>
                <th scope="col" colspan="2">Action</th>
                </tr>
            </thead>
            <tbody>
                {% for libraryDetail in libraryDetails %}
                    <tr>
                    <td style="display:none;">
                        <p class="library-type-info-json" style="display:none;"> {{libraryDetail|jsonstr}} </p>
                    </td>
                    <th scope="row">{{ forloop.counter }}</th>
                    <td>{{libraryDetail.libraryName}}</td>
                    <td style="width:10%;" >
                        <button type="button" class="btn btn-outline-warning update-library-type">
                        Update
                        </button>
                    </td>
                    <td style="width:10%;">
                        <p class="library-type-info" style="display:none;"> {{libraryDetail|jsonstr}} </p>
                        <button type="button" class="btn btn-outline-danger delete-library-type">
                            Delete
                        </button>
                    </td>
                    </tr>
                {% endfor %}
            </tbody>
            </table>
        </div>
        <div class="col-4" style="display:inline-block;padding:0%;float:right;">
        <div class="col text-center">
            <textarea class="form-control" id="library-type-name-textarea" rows="5" placeholder="Library Type Name"></textarea>
            <button id="add-library-type-button" type="button" class="btn btn-outline-primary col-md-6" style="margin-top:5%;">Add</button>
        </div>
        </div>
    </div>
    <div id="library-type-status-modal-container"></div>
</div>
<div id="update-library-type-popup-modal" class="modal fade bd-example-modal-xs" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xs">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> Update Library Type Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                  <div class="form-row">
                    <div class="form-group col-md-12">
                      <p style="display:none" id = "update-library-type-id"></p>
                        <input type="text" id="library-type-name-text" class="form-control col-8 mandatory-field" placeholder="Library Type Name...">
                    </div>
                  </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" id="update-library-type-button">Update</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-library-type-popup-modal" class="modal fade" tabindex="-1" role="dialog">
 <div class="modal-dialog" role="document">
   <div class="modal-content">
     <div class="modal-header">
       <h5 class="modal-title">Delete Library Type</h5>
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
       <p style="color:red;"> <strong> Are you sure to Delete Library Type? </strong></p>
     </div>
     <div class="modal-footer">
       <p id="delete-library-type-id" style="display:none;"></p>
       <button type="button" class="btn btn-danger" id="delete-library-type-button">Yes, Delete</button>
       <button type="button" class="btn btn-success" data-dismiss="modal">Review</button>
     </div>
   </div>
 </div>
</div>