{% load json %}
<p style="display:none;">{{publisherDetails|jsonstr}}</p>
<div class="card">
    <div class="card-body">
        <div class="col-8" style="display:inline-block">
            <table id="datatables-reponsive" class="table table-striped datatables-reponsive-table">
            <caption>List of Publishers</caption>
            <thead>
                <tr>
                <th scope="col">#</th>
                <th scope="col">Publisher Name</th>
                <th scope="col" colspan="3">Action</th>
                </tr>
            </thead>
            <tbody>
                {% for publisherDetail in publisherDetails %}
                    <tr>
                    <td style="display:none;">
                        <p class="publisher-info-json" style="display:none;"> {{publisherDetail|jsonstr}}  </p>
                    </td>
                    <th scope="row">{{ forloop.counter }}</th>
                    <td>{{publisherDetail.publisherName}}</td>
                    <td style="width:10%;" >
                        <button type="button" class="btn btn-outline-info view-publisher">
                        View
                        </button>
                    </td>
                    <td style="width:10%;" >
                        <button type="button" class="btn btn-outline-warning update-publisher">
                        Update
                        </button>
                    </td>
                    <td style="width:10%;">
                        <button type="button" class="btn btn-outline-danger delete-publisher">
                            Delete
                        </button>
                    </td>
                    </tr>
                {% endfor %}
            </tbody>
            </table>
        </div>
        <div class="col-4" style="display:inline-block;padding:0%;float:right;">
        <div class="col text-center">
            <div class="card" style="border: 2px solid #dee2e6; border-radius: 8px; padding: 20px; background-color: #f8f9fa;">
                <div class="card-header" style="background-color: #e9ecef; border-bottom: 1px solid #dee2e6; margin: -20px -20px 20px -20px; padding: 15px 20px; border-radius: 6px 6px 0 0;">
                    <h5 style="margin: 0; color: #495057; font-weight: bold;">Add Publisher</h5>
                </div>
                <form>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="publisher-name" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Publisher Name<span style="color: red;">*</span></label>
                        <input type="text" class="form-control" id="publisher-name" placeholder="Publisher Name" required style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="publisher-contact" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Contact Information</label>
                        <textarea class="form-control" id="publisher-contact" rows="2" placeholder="Contact Information" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px; resize: vertical;"></textarea>
                    </div>
                    <div class="form-group" style="text-align: left; margin-bottom: 20px;">
                        <label for="publisher-affiliation" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Affiliation</label>
                        <input type="text" class="form-control" id="publisher-affiliation" placeholder="Affiliation" style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                </form>
                <button id="add-publisher-button" type="button" class="btn btn-outline-primary" style="margin-top: 15px; padding: 10px 30px; font-weight: bold; border-radius: 5px;">Add Publisher</button>
            </div>
        </div>
        </div>
    </div>
    <div id="publisher-status-modal-container"></div>
</div>

<div id="view-publisher-popup-modal" class="modal fade bd-example-modal-xs" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xs">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">View Publisher Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group col-md-12">
                        <label>Publisher Name:</label>
                        <p id="view-publisher-name"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Contact Information:</label>
                        <p id="view-publisher-contact"></p>
                    </div>
                    <div class="form-group col-md-12">
                        <label>Affiliation:</label>
                        <p id="view-publisher-affiliation"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="update-publisher-popup-modal" class="modal fade bd-example-modal-xs" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xs">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> Update Publisher Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                  <div class="form-row">
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                      <p style="display:none" id = "update-publisher-id"></p>
                      <label for="publisher-name-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Publisher Name<span style="color: red;">*</span></label>
                        <input type="text" id="publisher-name-text" class="form-control mandatory-field" placeholder="Publisher Name..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="publisher-contact-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Contact Information</label>
                        <textarea id="publisher-contact-text" class="form-control" rows="2" placeholder="Contact Information..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px; resize: vertical;"></textarea>
                    </div>
                    <div class="form-group col-md-12" style="text-align: left; margin-bottom: 20px;">
                        <label for="publisher-affiliation-text" style="font-weight: bold; color: #495057; margin-bottom: 8px; display: block;">Affiliation</label>
                        <input type="text" id="publisher-affiliation-text" class="form-control" placeholder="Affiliation..." style="border: 1px solid #ced4da; border-radius: 4px; padding: 10px;">
                    </div>
                  </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" id="update-publisher-button">Update</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-publisher-popup-modal" class="modal fade" tabindex="-1" role="dialog">
 <div class="modal-dialog" role="document">
   <div class="modal-content">
     <div class="modal-header">
       <h5 class="modal-title">Delete Publisher</h5>
       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
         <span aria-hidden="true">&times;</span>
       </button>
     </div>
     <div class="modal-body">
       <p style="color:red;"> <strong> Are you sure to Delete Publisher? </strong></p>
     </div>
     <div class="modal-footer">
       <p id="delete-publisher-id" style="display:none;"></p>
       <button type="button" class="btn btn-danger" id="delete-publisher-button">Yes, Delete</button>
       <button type="button" class="btn btn-success" data-dismiss="modal">Review</button>
     </div>
   </div>
 </div>
</div>