{% load json %}
<div class="row">
    <p id="book-id" style="display: none;"></p>
    <div class="mb-3 col-md-4">
      <label>Book Title*</label>
      <input type="text" class="form-control mandatory-field" id="book-title" placeholder="Enter book title ...">
    </div>

    <div class="mb-3 col-md-4">
      <label>Book Number</label>
      <input type="text" class="form-control" id="book-number" placeholder="Enter book number ...">
    </div>

    <div class="mb-3 col-md-4">
      <label>Genre</label>
      <select class="custom-select mr-sm-4" id="book-genre">
        <option value="" selected>Choose genre ...</option>
        {% for genreDetail in genreDetails %}
          <option value={{genreDetail.genreId}}>{{genreDetail.genreName}}({{genreDetail.classificationNumber}})</option>
        {% endfor%}
      </select>
    </div>
</div>

  <div class="row">
    <div class="mb-3 col-md-4">
      <label>Isbn Number</label>
      <input type="text" class="form-control" id="book-isbn-number" placeholder="Enter Isbn Number ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>Publication</label>
      <select class="custom-select mr-sm-4" id="book-publication">
        <option value="" selected>Choose publication ...</option>
        {% for publicationDetail in publicationDetails %}
          <option value={{publicationDetail.publicationId}}>{{publicationDetail.publicationName}}</option>
        {% endfor%}
      </select>
    </div>
    <div class="mb-3 col-md-4">
      <label>Publisher</label>
      <select class="custom-select mr-sm-4" id="book-publisher">
        <option value="" selected>Choose publisher ...</option>
        {% for publisherDetail in publisherDetails %}
          <option value={{publisherDetail.publisherId}}>{{publisherDetail.publisherName}}</option>
        {% endfor%}
      </select>
    </div>
  </div>

  <div class="row">
    <div class="mb-3 col-md-4">
      <label>Number Of Copies*</label>
      <input type="number" class="form-control mandatory-field" id="book-copies" placeholder="Enter book no. of copies ...">
      <span style="color:red" id="copies-error"></span>
    </div>
    <div class="mb-3 col-md-4">
      <label>Author</label>
      <select class="custom-select mr-sm-4" id="book-author">
        <option value="" selected>Choose author ...</option>
        {% for authorDetail in authorDetails %}
          <option value={{authorDetail.authorId}}>{{authorDetail.authorName}}</option>
        {% endfor%}
      </select>
    </div>
    <div class="mb-3 col-md-4">
      <label>Edition</label>
      <input type="text" class="form-control" id="book-edition" placeholder="Enter book edition ...">
    </div>
  </div>
  <div class="row">
    <div class="mb-3 col-md-4">
      <label>Publication Year</label>
      <input type="text" class="form-control" id="book-publication-year" placeholder="Enter book publication year ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>Language</label>
      <input type="text" class="form-control" id="book-language" placeholder="Enter book language ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>Tags</label>
      <input type="text" class="form-control" id="book-tags" placeholder="Enter book tags eg(Thriller, Comedy) ...">
    </div>
  </div>
  <div class="row">
    <div class="mb-3 col-md-4">
      <label>Type Of Binding</label>
      <input type="text" class="form-control" id="book-binding" placeholder="Enter book binding ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>Number Of Pages</label>
      <input type="number" class="form-control" id="book-pages" placeholder="Enter book pages ...">
    </div>
    <div class="mb-3 col-md-4" id="update-book-photo-label">
      <label>Upload Cover Image</label>
      <input type="file" class="form-control" id="book-cover-image" name="docfile" style="height: 35%;">
      <p style="color:red">File size should be less than 500 kb.</p>
    </div>
  </div>
  <div class="vendor-details" style="display: none;">{{vendorDetails|jsonstr}}</div>
  <div class="library-type-details" style="display: none;">{{libraryTypeDetails|jsonstr}}</div>
  <div class="library-accession-number-access" style="display: none;">{{accessionNumberAccess|jsonstr}}</div>
  {% if not accessionNumberAccess%}
  <div class="row">
    <div class="mb-3 col-md-4">
      <label>Bill Number</label>
      <input type="text" class="form-control" id="book-bill-number" placeholder="Enter book price ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>Date Of Purchase</label>
      <input type=text class="form-control select-date book-purchase-date" placeholder="Purchase Date"/>
    </div>
    <div class="mb-3 col-md-4">
      <label>Vendor</label>
      <select class="custom-select mr-sm-4" id="book-vendor">
        <option value="" selected>Choose vendor ...</option>
        {% for vendorDetail in vendorDetails %}
          <option value={{vendorDetail.vendorId}}>{{vendorDetail.vendorName}}</option>
        {% endfor%}
      </select>
    </div>
  </div>
  <div class="row">
    <div class="mb-3 col-md-4">
      <label>Volume</label>
      <input type="text" class="form-control" id="book-volume" placeholder="Enter book volume ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>Rack</label>
      <input type="text" class="form-control" id="book-rack" placeholder="Enter book rack ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>Library Type</label>
      <select class="custom-select mr-sm-4" id="book-library-type">
        <option value="" selected>Choose library type ...</option>
        {% for libraryType in libraryTypeDetails %}
          <option value={{libraryType.libraryTypeId}}>{{libraryType.libraryName}}</option>
        {% endfor%}
      </select>
    </div>
  </div>
  <div class="row">
    <div class="mb-3 col-md-4">
      <label>Purchage Price Per Copy</label>
      <input type="text" class="form-control" id="book-price" placeholder="Enter book price ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>MRP</label>
      <input type="text" class="form-control" id="book-mrp" placeholder="Enter book mrp ...">
    </div>
    <div class="mb-3 col-md-4">
      <label>Added On</label>
      <input type=text class="form-control select-date book-added-on-date" placeholder="Addedd On Date"/>
    </div>
  </div>
  <div class="row">
    <div class="mb-3 col-md-4">
      <label>Status</label>
      <select class="custom-select mr-sm-4" id="book-status">
        <option value="ACTIVE" selected>ACTIVE</option>
        <option value="INACTIVE">INACTIVE</option>
      </select>
    </div>
    <div class="mb-3 col-md-4">
      <label>Remarks</label>
      <textarea class="form-control" id="book-remark" placeholder="Remarks"></textarea>
    </div>
  </div>
  {% else %}
  <div class='individualBookDetails' style="overflow-x: auto; white-space: nowrap;">
    
  </div>
  {% endif %}
