{% load json %}
<div class="row mb-2 mb-xl-3">
  {% include 'core/utils/v2/academic_session_display.html'%}
      <div class="col-auto ml-auto text-right mt-n1">
        <button type="button" class="btn btn-primary mr-2" onclick="manageStandards.openEditStandardsModal()">Edit Standards</button>
        {% include 'core/utils/v2/academic_session_dropdown.html'%}
      </div>
</div>

<p id="edit-standards-data" style="display:none;">
  {{institute_standard_sections_list|jsonstr}}
</p>
<br/>
<div class="card">
  <div class="card-header">
  </div>
  <div id="manage-sections-list" class="card-body">
    {% include 'institute_management/manage_sections/manage_sections_list.html' %}
  </div>
</div>

<div id="update-manage-sections-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="update.standard-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Update Sections</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                    <div id="update.standard-sections-id">

                   </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="manageSections.updateSectionsDetails()">Update Section Name</button>
            </div>
        </div>
    </div>
</div>

<div id="add-manage-sections-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="add.standard-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Add Sections</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">

                    <div id="add.standard-sections-id">

                    </div>

                    <div>
                        <a style="text-decoration:underline;cursor:pointer;" class="mt-1" onclick="manageSections.addMoreClass(this);">+Add More</a>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="manageSections.addSectionsDetails()">Add Sections</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-id-manage-sections-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="add.standard-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Delete Sections</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                  <div id="delete.standard-sections-manage-id">

                  </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="manageSections.deleteStudentSectionsDetails()">Confirm</button>
            </div>
        </div>
    </div>
</div>

<div id="delete-manage-sections-modal" class="modal fade bd-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <p style="display:none;" id="delete.standard-id"></p>
            <div class="modal-header">
                <h5 class="modal-title" id="update.transport-area-title">Delete Sections</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal()">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form class="add-stock-form">
                    <div id="delete.standard-sections-id">

                    </div>
                    <button type="button" class="btn btn-danger ml-4" name="button" onclick="manageSections.deleteSectionDetails()">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div id="edit-standards-modal" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Edit Standards</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"> &times; </span>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-standards-form">
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <label>Academic Session*</label>
                            <input type="text" class="form-control" id="edit-standards-session-display" placeholder="" readonly>
                        </div>
                    </div>
                </form>
                <h5>Edit Standards</h5>
                <div class="row">
                    <div class="col-md-1"></div>
                    <div class="col-md-10">
                        <table class="table borderless">
                            <thead>
                                <tr>
                                    <th style="width:50px;"></th>
                                    <th scope="col">Standard Name</th>
                                    <th scope="col">Stream</th>
                                    <th scope="col">Level</th>
                                    <th scope="col"></th>
                                </tr>
                            </thead>
                            <tbody id="edit-standards-input-div">
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-1"></div>
                </div>
                <!-- <div style="display: flex; align-items: center; justify-content: center;">
                    <button type="button" class="btn btn-info" onclick='manageStandards.addNewStandardRow()'>Add More Standards</button>
                </div> -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                <button id="edit-standards-button" type="button" class="btn btn-primary" onclick="manageStandards.updateStandards()">Update Standards</button>
            </div>
        </div>
    </div>
</div>

<div id="manage-sections.status-modal-container"></div>
