$(document).ready(function() {
    menuLoader.registerSidebarMenu();
    homePage.initHomePage();
    paymentReminder.readPaymentState();
});

var menuLoader = {

  registerSidebarMenu : function () {
      sideBarHoverEventCallback();
      activateMenuItem();
      menuLoader.registerHomeMenu();
      menuLoader.registerManageSectionsMenu();
      menuLoader.registerManageClassTeachersMenu();
      menuLoader.registerManageInstituteBankDetailsNav();
      menuLoader.registerManageInstituteDetailsNav();
      menuLoader.registerManageInstituteLogoNav();
  },

  registerHomeMenu : function () {
    $('#homeNav').on('click', function() {
      homePage.loadHomePage();
    });
  },

  registerManageSectionsMenu : function(){
    $('#manageSectionsNav').on('click', function(){
        manageSections.loadManageSectionsPage();
    });
  },

  registerManageClassTeachersMenu : function(){
    $('#manageClassTeachersNav').on('click', function(){
        manageClassTeachers.loadManageClassTeachersPage();
    });
  },

  registerManageInstituteBankDetailsNav : function(){
    $('#manageInstituteBankDetailsNav').on('click', function(){
         manageInstituteBankAccountDetails.loadManageInstituteBankAccounntDetailsPage();
    });
    },

  registerManageInstituteDetailsNav : function(){
  $('#manageInstituteDetailsNav').on('click', function(){
       manageInstituteDetails.loadManageInstituteDetailsPage();
  });
  },

  registerManageInstituteLogoNav : function(){
  $('#manageInstituteLogoNav').on('click', function(){
       manageInstituteLogo.loadManageInstituteLogoPage();
  });
  }
};

// ----------------------------------------- Home Page ------------------------------ //
var homePage =  {

  initHomePage : function () {
      academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
  },

  loadHomePage : function () {
    ajaxClient.get("/institute-management/home", function(data) {
        $("#main-content").html(data);
        academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession);
    });
  },

  loadHomePageForSession : function () {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/institute-management/session-home/"+academicSessionId, function(data) {
        $("#institute-management-dashboard-session-content").html(data);
    });
  },

  refreshHomePage : function () {
      homePage.loadHomePageForSession();
  },
};


// ----------------------------------------- Manage Sections ------------------------------ //
var manageSections = {

  dataCache : {},

  loadManageSectionsPage : function(){

    ajaxClient.get("/institute-management/manage-sections-home", function(data) {
        $("#main-content").html(data);
        manageSections.initPage();
        academicSessionHandler.bindSessionChangeEvent(manageSections.changeSession);
    });
  },

  initPage : function(){
    manageSections.registerUpdateSectionsModal();
    manageSections.registerAddSectionsModal();
    manageSections.registerDeleteSectionsModal();
  },

  clearCache : function(){
      manageSections.dataCache = {}
  },

  changeSession : function(){
      manageSections.loadManageSectionslist();
  },

  loadManageSectionslist : function() {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/institute-management/manage-sections/"+academicSessionId, function(data) {
        $("#manage-sections-list").html(data);
          manageSections.initPage();
          manageSections.clearCache();
    });
  },

  registerUpdateSectionsModal : function(){
    $('.update-manage-sections').on('click',function(){
      var manageSectionsJson = $(this).parent().find('.manage-sections-info').text().trim();
      var sectionDetails = JSON.parse(manageSectionsJson);
      manageSections.dataCache.sectionDetails = sectionDetails;
      $('#update\\.standard-id').val(sectionDetails.standardId);
      var sectionNameList = "";
      for(var i = 0;i<sectionDetails.standardSectionList.length;i++){
        var sectionName = sectionDetails.standardSectionList[i].sectionName;
        html = "<div class=\"form-group  col-md-8\"><input pattern=\"[A-Z]{1}\" type=\"text\" class=\"form-control update-rename-manage-sections add-label-input mandatory-field update.section-name\" placeholder=\"Enter Section Name\"   value="+sectionName+"></div>";
        sectionNameList += html;
      }

      $('#update\\.standard-sections-id').html(sectionNameList);
      $("#update-manage-sections-modal").modal('toggle');
    });
  },

  registerAddSectionsModal : function(){
    $('.add-manage-sections').on('click',function(){
        var manageSectionsJson = $(this).parent().find('.manage-sections-info').text().trim();
        var sectionDetails = JSON.parse(manageSectionsJson);
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        var standardId = sectionDetails.standardId;
        $('#add\\.standard-id').val(standardId);
        ajaxClient.get("/institute-management/class-students/"+academicSessionId +"?standardId="+standardId, function(data) {
            $("#manage-sections-list").html(data);
            var studentData = $('#class-students').text();
            var sectionNameList = "";
            if(studentData!="None"  && sectionDetails.standardSectionList.length != 0){
              for(var i = 0;i<sectionDetails.standardSectionList.length;i++){
                var sectionName = sectionDetails.standardSectionList[i].sectionName;
                html = "<div style=\"display:none;\"><p style=\"display:none;\" id=\"include-radio\">false</p><div class=\"form-group row sections-remove col-md-10\"><input id=\"sectionInput\" pattern=\"[A-Z]{1}\" type=\"text\" class=\"form-control col-md-9 hidden-rename-manage-sections add-label-input mandatory-field add.section-name\" placeholder=\"Enter Section Name\"  value="+sectionName+" readonly></div></div>";
                sectionNameList += html;
              }
              html = "<p style=\"display:none;\" id=\"include-radio\">false</p><div class=\"form-group row sections-remove col-md-8\"><input id=\"sectionInput\" pattern=\"[A-Z]{1}\" type=\"text\" class=\"form-control col-md-9 add-rename-manage-sections add-label-input mandatory-field add.section-name\" placeholder=\"Enter Section Name\"></div>";
              sectionNameList += html;
            }
            else if(studentData!="None" && sectionDetails.standardSectionList.length == 0){
              html = "<p style=\"display:none;\" id=\"include-radio\">true</p><div class=\"form-group row sections-remove col-md-12\"><input id=\"sectionInput\" pattern=\"[A-Z]{1}\" type=\"text\" class=\"form-control add-rename-manage-sections col-md-6 add-label-input mandatory-field add.section-name\" placeholder=\"Enter Section Name\" ><div class=\"mt-2\"><input type=\"radio\" name=\"standard\" class=\"ml-0 pt-2 standard mt-0 mandatory-field\"><p style=\"display:inline-block\" class=\"mb-0 pl-1\">Move Existing Students to this section</p></div></div>";
              sectionNameList += html;
            }
            else if(studentData=="None" && (sectionDetails.standardSectionList.length != 0||sectionDetails.standardSectionList.length == 0)){
              html = "<p style=\"display:none;\" id=\"include-radio\">false</p><div class=\"form-group row sections-remove col-md-8\"><input id=\"sectionInput\" pattern=\"[A-Z]{1}\" type=\"text\" class=\"form-control add-rename-manage-sections col-md-9 add-label-input mandatory-field add.section-name\" placeholder=\"Enter Section Name\" ></div>";
              sectionNameList += html;
            }
            $('#add\\.standard-sections-id').html(sectionNameList);
            $('#add-manage-sections-modal').modal('toggle');
            manageSections.initPage();
            manageSections.clearCache();
        });
    });
  },

  addMoreClass : function(ref){
      var includeRadio = $('#include-radio').html();
      if(includeRadio == "true"){
        html = $('#add\\.standard-sections-id').html();
        newHtml = "<div class=\"form-group row sections-remove col-md-12\"><input id=\"sectionInput\" pattern=\"[A-Z]{1}\" type=\"text\" class=\"form-control add-rename-manage-sections col-md-6 mt-1 add-label-input mandatory-field add.section-name\" placeholder=\"Enter Section Name\" ><button type=\"button\" class=\"col-md-2 mt-1 close\" data-dismiss=\"modal\" aria-label=\"Close\" onclick=\"manageSections.removeSectionDetailsRow(this);\"><span aria-hidden=\"true\"> &times; </span></button><div class=\"mt-2\"><input type=\"radio\" name=\"standard\" class=\"ml-0 standard mt-0 pt-2 mandatory-field\"><p style=\"display:inline-block\" class=\"pl-1\">Move Existing Students to this section</p></div></div>";

        $('#add\\.standard-sections-id').append(newHtml);
      }
      else{
        html = $('#add\\.standard-sections-id').html();
        newHtml = "<p style=\"display:none;\" id=\"include-radio\">false</p><div class=\"form-group row sections-remove col-md-8\"><input id=\"sectionInput\" pattern=\"[A-Z]{1}\" type=\"text\" class=\"form-control col-md-9 add-rename-manage-sections add-label-input mandatory-field add.section-name\" placeholder=\"Enter Section Name\"  value=\"\"><button type=\"button\" class=\"col-md-2 close\" data-dismiss=\"modal\" aria-label=\"Close\" onclick=\"manageSections.removeSectionDetailsRow(this)\"><span aria-hidden=\"true\"> × </span></button></div>";
        $('#add\\.standard-sections-id').append(newHtml);
      }
      // manageSections.initPage();
  },

  removeSectionDetailsRow : function (ref) {
    $(ref).parent().remove()
    // manageSections.addMoreClass();
  },


  addSectionsDetails: function(){
    var invalid = validations($("#add-manage-sections-modal"));
    if(invalid){
      return;
    }
    var isRadio =  $('#include-radio').html();
    var sectionNames = getAddSectionsName();
    var unique = Array.from(new Set(sectionNames));
    var hiddenSectionNames = getHiddenSectionNames();
    var studentsSectionName = "";
    if(sectionNames.length != unique.length){
        $('#add-manage-sections-modal').modal('toggle');
        showErrorDialogBox("Duplicate Section Names");
        return;
    }
    if(isRadio == "false"){
        var commonSections = commonBetweenTwoNames(sectionNames,hiddenSectionNames);
        var sections = ""
        for(var i=0;i<hiddenSectionNames.length;i++){
          sections += hiddenSectionNames[i]
          if(hiddenSectionNames.length>i+1){
            sections+=","
          }
        }
        if(commonSections.length!=0){
            $('#add-manage-sections-modal').modal('toggle');
            showErrorDialogBox("Section " + sections + " are already present.Please write a different section names");
            return;
        }
    }

          var standardSectionsList = []
          var studentsSectionName = $('input.standard:checked').parent().parent().find('.add-rename-manage-sections').val();
          if(isRadio == "true"){
              if(studentsSectionName == "" || studentsSectionName == undefined){
                  $('#add-manage-sections-modal').modal('toggle');
                showErrorDialogBox("Please Select the section you want the students to move");
                return;
              }
          }

          var academicSessionId = academicSessionHandler.getSelectedSessionId();
          var standardId = $('#add\\.standard-id').val();

          for(var i=0;i<sectionNames.length;i++){
            section_dict = {"sectionId":null,"sectionName":sectionNames[i],"studentCount":null}
            standardSectionsList.push(section_dict);
          }

          $('#add-manage-sections-modal').modal('toggle');
          var bulkAddStudentSections = {"instituteId":null,"academicSessionId":academicSessionId,"standardId":standardId,"standardSectionsList":standardSectionsList,"moveStudentToSection":studentsSectionName}

          ajaxClient.post("/institute-management/bulk-add-students-section/"+academicSessionId,{'bulkAddStudentSections':JSON.stringify(bulkAddStudentSections)},function(data){
            $("#manage-sections\\.status-modal-container").html(data);
            $("#manage-sections\\.status-modal").modal('toggle');
             manageSections.loadManageSectionslist();
          });
        },


  registerDeleteSectionsModal : function(){
  $('.delete-manage-sections').on('click',function(){
    var manageSectionsJson = $(this).parent().find('.manage-sections-info').text().trim();
    var sectionDetails = JSON.parse(manageSectionsJson);
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId = sectionDetails.standardId;
    $('#delete\\.standard-id').val(standardId);
    var sectionNameList = "";
    for(var i = 0;i<sectionDetails.standardSectionList.length;i++){
      var sectionName = sectionDetails.standardSectionList[i].sectionName;
      var sectionId = sectionDetails.standardSectionList[i].sectionId;
      html = "<p style=\"display:none;\" id=\"include-radio\">false</p><div class=\"form-group row sections-remove col-md-8\"><input type=\"checkbox\" class=\"section-id\" name=\"\" value=\"\"><p class=\"bulk-section-id\" style=\"display:none;\">"+sectionId +"</p><input id=\"sectionInput\" pattern=\"[A-Z]{1}\" type=\"text\" class=\"form-control ml-2 delete-rename-manage-sections add-label-input mandatory-field delete.section-name col-md-8\" placeholder=\"Enter Section Name\"  value="+sectionName+" readonly></div>";
      sectionNameList += html;
    }
    $('#delete\\.standard-sections-id').html(sectionNameList);
    $('#delete-manage-sections-modal').modal('toggle');
    manageSections.initPage();
    manageSections.clearCache();
  });
  },

  deleteSectionDetails: function(){

    var invalid = validations($("#delete-manage-sections-modal"));
    if(invalid){
      return;
    }
     var section_info_dict = manageSections.getDictionarySectionInfos();
     var academicSessionId = academicSessionHandler.getSelectedSessionId();
     var standardId = $('#delete\\.standard-id').val();

     options = "";
     checkedSection = section_info_dict[0];
     unCheckedSection = section_info_dict[1];

     if(checkedSection.length == 0){
         $('#delete-manage-sections-modal').modal('toggle');
       showErrorDialogBox("Please Select atleast one section");
       return
     }

     if(unCheckedSection.length!=0){
       html1 = "";
       for(var i=0;i< unCheckedSection.length;i++){
            html1 = "<option value="+unCheckedSection[i].sectionId +">"+unCheckedSection[i].sectionName+"</option>";
            options+=html1;
       }
       mainhtml = "<p>Where do you wish to move them?</p>" + "<select class=\"col-md-6 form-control which-section mandatory-field\" name=\"\">"+options+"</select>";
     }
     else{
       mainhtml = "<p>Do you wish to move the students to this Standard?</p>";
     }
      $('#delete-manage-sections-modal').modal('toggle');


     var section_ids = [];
     var checkedSectionIds = [];
     for(var i=0;i< checkedSection.length; i++){
        sectionId = checkedSection[i].sectionId
        sectionName = checkedSection[i].sectionName
        section_ids.push(sectionId)
        checkedSectionIds.push(sectionName);
     }

     section_ids = section_ids.join(",");
     manageSections.dataCache.sectionIds = section_ids

     ajaxClient.get("/institute-management/class-students-sections-list/"+academicSessionId+"?standardId="+standardId+"&sectionIds="+section_ids,function(data){
       $("#manage-sections-list").html(data);
       var studentJson = JSON.parse($('#class-students').html());
       if(studentJson.length == 0){
         html = "<p style=\"display:none;\" class=\"std-id\">"+standardId+"</p>"+"Do you wish to delete the section names?";
       }
       else{
         html = "<p style=\"display:none;\" class=\"std-id\">"+standardId+"</p><p>You have selected section "+ checkedSectionIds.join(", ") +" to be deleted</p><p>" + String(studentJson.length) + " students are present in the selected section to be deleted.</p>"+mainhtml;

       }
          $('#delete\\.standard-sections-manage-id').html(html);
         $('#delete-id-manage-sections-modal').modal('toggle');
         manageSections.initPage();

     });
  },


  deleteStudentSectionsDetails : function(){
    $('#delete-id-manage-sections-modal').modal('toggle');
    var invalid = validations($("#delete-id-manage-sections-modal"));
    if(invalid){
      return;
    }

    var student_assign_section_id = $('select.which-section').val();
    if (student_assign_section_id == undefined ||student_assign_section_id == null){
      student_assign_section_id = 0;
    }

     section_ids = manageSections.dataCache.sectionIds;
     var academicSessionId = academicSessionHandler.getSelectedSessionId();
     var standardId = $('.std-id').html();

    ajaxClient.post("/institute-management/bulk-delete-students-section/"+academicSessionId+"/"+standardId+"/"+section_ids+"/"+student_assign_section_id,{},function(data){
      $("#manage-sections\\.status-modal-container").html(data);
      $("#manage-sections\\.status-modal").modal('toggle');
      // clearSectionName();
       manageSections.loadManageSectionslist();
    });
  },

  getDictionarySectionInfos : function(){
    var finalList = [];
    var unCheckedSectionList = [];
    var checkedSectionList = [];
      $("input.section-id").each(function() {
          if(!$(this).is(":checked")) {
            sectionName = $(this).parent().find('input.delete-rename-manage-sections').val();
            sectionId = $(this).parent().find('p.bulk-section-id').first().text().trim();
            section_dict = {"sectionId":sectionId,"sectionName":sectionName}
            unCheckedSectionList.push(section_dict);
          }
          else{
            sectionName = $(this).parent().find('input.delete-rename-manage-sections').val();
            sectionId = $(this).parent().find('p.bulk-section-id').first().text().trim();
            section_dict = {"sectionId":sectionId,"sectionName":sectionName}
            checkedSectionList.push(section_dict);
          }
      });
      finalList.push(checkedSectionList);
      finalList.push(unCheckedSectionList);
      return finalList;
  },

  updateSectionsDetails : function(){
    var invalid = validations($("#update-manage-sections-modal"));
    if(invalid){
      return;
    }
    var sectionNames= [];
    sectionNames = getUpdateSectionsName();
    var unique = Array.from(new Set(sectionNames));
    if(sectionNames.length != unique.length){
      $("#update-manage-sections-modal").modal('toggle');
      showErrorDialogBox("Duplicate Section Names");
      return;
    }
    var standardSectionsList = []
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId = $('#update\\.standard-id').val();
    var standardSectionList = manageSections.dataCache.sectionDetails.standardSectionList;
    for(var i=0;i<standardSectionList.length;i++){
      section_info_dict = {"sectionId":standardSectionList[i].sectionId,"sectionName":sectionNames[i],"studentCount":standardSectionList[i].studentCount}
      standardSectionsList.push(section_info_dict);
    }
    $("#update-manage-sections-modal").modal('toggle');
    var bulkUpdateStudentSections = {"instituteId":null,"academicSessionId":academicSessionId,"standardId":standardId,"standardSectionsList":standardSectionsList}
    ajaxClient.post("/institute-management/bulk-update-students-section/"+academicSessionId,{'bulkUpdateStudentSections':JSON.stringify(bulkUpdateStudentSections)},function(data){
      $("#manage-sections\\.status-modal-container").html(data);
      $("#manage-sections\\.status-modal").modal('toggle');
       manageSections.loadManageSectionslist();
    });
  },
};

// ----------------------------------------- Manage Class Teachers ------------------------------ //
var manageClassTeachers = {

  dataCache : {},

  loadManageClassTeachersPage : function(){

    ajaxClient.get("/institute-management/manage-class-teachers-home", function(data) {
        $("#main-content").html(data);
        manageClassTeachers.initPage();
        academicSessionHandler.bindSessionChangeEvent(manageClassTeachers.changeSession);
    });
  },

  initPage : function(){
    manageClassTeachers.registerUpdateClassTeachersModal();
    manageClassTeachers.registerDeleteClassTeachersModal();
    documentHandler.registerUploadDocumentCallBack();
  },

  clearCache : function(){
      manageClassTeachers.dataCache = {}
  },

  changeSession : function(){
      manageClassTeachers.loadManageClassTeacherslist();
  },

  loadManageClassTeacherslist : function() {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/institute-management/manage-class-teachers/"+academicSessionId, function(data) {
        $("#manage-class-teachers-list").html(data);
          manageClassTeachers.initPage();
          manageClassTeachers.clearCache();
    });
  },

  registerUpdateClassTeachersModal : function(){
    $('.update-manage-class-teachers').on('click',function(){
      $('.staff-details-dropdown').val("");
      var manageClassTeacherJson = $(this).parent().find('.manage-standard-session-data-json').text().trim();
      $("#update-standard-staff-details-json").text(manageClassTeacherJson);

      var manageClassTeacher = JSON.parse(manageClassTeacherJson);

      if(manageClassTeacher != null && manageClassTeacher != "" && manageClassTeacher != undefined) {
        var staffLite = manageClassTeacher.staffLite;
        if(staffLite != null && staffLite != "" && staffLite != undefined) {
          var staffId = staffLite.staffId;
          $('.staff-details-dropdown').val(staffId);
        }
      }
    });
  },

  updateClassTeachersDetails : function(ref){
    var invalid = validations($("#update-manage-class-teachers-modal"));
    if(invalid){
      return;
    }
    var manageClassTeacherJson = $("#update-standard-staff-details-json").text();
    var manageClassTeacher = JSON.parse(manageClassTeacherJson);

    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId = manageClassTeacher.standardRowDetails.standardId;
    var sectionId = manageClassTeacher.standardRowDetails.sectionId;
    if(sectionId == null || sectionId == undefined) {
      sectionId = "";
    }
    var staffId = $(ref).parent().parent().find('.staff-details-dropdown').val();

    $("#update-manage-class-teachers-modal").modal('toggle');

    ajaxClient.post("/institute-management/update-class-teacher-details/"+academicSessionId+"/"+standardId+"/"+staffId+"?section_id="+sectionId,{},function(data){
      $("#manage-class-teachers-status-modal-container").html(data);
      $("#manage-class-teachers-status-modal").modal('toggle');
       manageClassTeachers.loadManageClassTeacherslist();
    });
  },

  registerDeleteClassTeachersModal : function(){
  $('.delete-manage-class-teachers').on('click',function(){
    var manageClassTeacherJson = $(this).parent().find('.manage-standard-session-data-json').text().trim();
    $("#delete-standard-staff-details-json").text(manageClassTeacherJson);
  });
  },

  deleteClassTeachersDetails : function(ref){
    var manageClassTeacherJson = $("#delete-standard-staff-details-json").text();
    var manageClassTeacher = JSON.parse(manageClassTeacherJson);
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId = manageClassTeacher.standardRowDetails.standardId;
    var sectionId = manageClassTeacher.standardRowDetails.sectionId;
    if(sectionId == null || sectionId == undefined) {
      sectionId = "";
    }
    var staffId = $(ref).parent().parent().find('.staff-details-dropdown').val();

    $("#delete-manage-class-teachers-modal").modal('toggle');

    ajaxClient.post("/institute-management/delete-class-teacher-details/"+academicSessionId+"/"+standardId+"?section_id="+sectionId,{},function(data){
      $("#manage-class-teachers-status-modal-container").html(data);
      $("#manage-class-teachers-status-modal").modal('toggle');
       manageClassTeachers.loadManageClassTeacherslist();
    });
  },

};

function getUpdateSectionsName(){
  var sectionNames = [];
  $(".update-rename-manage-sections").each(function(){
     var sectionName = $(this).val().trim();
     sectionNames.push(sectionName);
  });
  return sectionNames;
}

function getAddSectionsName(){
  var sectionNames = [];
  $(".add-rename-manage-sections").each(function(){
     var sectionName = $(this).val().trim();
     sectionNames.push(sectionName);
  });
  return sectionNames;
}

function getHiddenSectionNames(){
  var hiddenSectionNames = [];
  $(".hidden-rename-manage-sections").each(function(){
     var hiddenSectionName = $(this).val().trim();
     hiddenSectionNames.push(hiddenSectionName);
  });
  return hiddenSectionNames;
}

function commonBetweenTwoNames(sectionNames,hiddenSectionNames){
  var commonSections = [];
  for(var i=0;i<sectionNames.length;i++){
    for(var j=0;j<hiddenSectionNames.length;j++){
      if(sectionNames[i].trim() == hiddenSectionNames[j].trim()){
        commonSections.push(sectionNames[i]);
      }
    }
  }
  return commonSections;
}


function validations(element){
  clearValidationErrorDisplay();
  var invalid = false;
  $(element).find("input.mandatory-field").each(function() {
      if ($(this).val() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("select.mandatory-field").each(function() {
      if ($(this).find(':selected').length == 0 || $(this).find(':selected').val().trim() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-field-text\"> <span style=\"color:#e65f76;\">This field is mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.mandatory-table-field").each(function() {
      if ($(this).val() == "") {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"mandatory-table-field-text\"> <span style=\"color:#e65f76;\">Mandatory</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.hour-range").each(function() {
      if ($(this).val() < 0 || $(this).val() > 23) {
          $(this).css("border", "1px solid #ff8795");
          // $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.minute-range").each(function() {
      if ($(this).val() < 0 || $(this).val() > 59) {
          $(this).css("border", "1px solid #ff8795");
          // $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
          invalid = true;
      }
  });
  $(element).find("input.positive-number").each(function() {
      if ($(this).val() < 0) {
          $(this).css("border", "1px solid #ff8795");
          $(this).after("<p class=\"invalid-input-text\"> <span style=\"color:#e65f76;\">Invalid</span></p>");
          invalid = true;
      }
  });
  return invalid;
}

function clearValidationErrorDisplay(){

  $(".hour-range").css("border", "");
  $(".minute-range").css("border", "");
  $(".positive-number").css("border", "");
  $(".invalid-input-text").remove();

  $(".mandatory-field").css("border", "");
  $(".mandatory-field-text").remove();

  $(".mandatory-table-field").css("border", "");
  $(".mandatory-table-field-text").remove();

}

function closeModal(){
  clearValidationErrorDisplay();
}

  // ----------------------------------------- Manage Institute Bank Account Details ------------------------------ //

var manageInstituteBankAccountDetails = {

  dataCache : {},

  loadManageInstituteBankAccounntDetailsPage : function(data){
         ajaxClient.get("/institute-management/manage-institute-bank-account-details-home", function(data) {
         $("#main-content").html(data);
         manageInstituteBankAccountDetails.addNewBankAccountCallback();
         manageInstituteBankAccountDetails.initPage();
      });
  },

  initPage : function(){
    manageInstituteBankAccountDetails.viewBankAccountDetails();
    manageInstituteBankAccountDetails.updateBankAccountDetails();
    manageInstituteBankAccountDetails.registerDeleteBankAccountCallBack();
    $('#datatables-reponsive-1').DataTable( {
      "paging":   false,
      searching: false,
      columnDefs: [
        { targets: "no-sort", orderable: false },
      ],
      order: [[0, 'asc']]
    });
  },

  resetAddNewBankDetailsModal : function () {
    $("#add-bank-account-modal").find('input').val("");
  },

  loadBankAccountlist : function (){
    ajaxClient.get("/institute-management/bank-account-details", function(data) {
        $("#bank-account-list").html(data);
        manageInstituteBankAccountDetails.initPage();
    });
  },

  addNewBankAccountCallback : function (){
    $('#add-bank-account').on('click', function () {
      var invalid = validations($("#add-bank-account-modal"));
      if(invalid){
        return;
      }
        var bankName = $("#bank-name").val().trim();
        var branchName = $("#bank-branch-name").val().trim();
        var accountNumber = $("#account-number").val().trim();
        var accountHolderName = $("#account-holder-name").val().trim();
        var ifscCode = $("#ifsc-code").val().trim();
        var accountType = $("#account-type").val().trim() || null;
        var isPrimary = $("#is-primary").is(":checked");
        var isActive = $("#is-active").is(":checked")? "ACTIVE" : "INACTIVE";
        var instituteBankAccountDetailsPayload = {"accountType": accountType,"bankName":bankName,"branchName":branchName,"accountHolderName":accountHolderName,"accountNumber":accountNumber,"ifscCode":ifscCode,"status":isActive,"primary":isPrimary};
        $("#add-bank-account-modal").modal('toggle');
        ajaxClient.post("/institute-management/add-bank-account",{'instituteBankAccountDetailsPayload':JSON.stringify(instituteBankAccountDetailsPayload)}, function(data){
          $("#bank-account\\.status-modal-container").html(data);
          $("#bank-account\\.status-modal").modal('toggle');
          $("#account-type").val("");
          manageInstituteBankAccountDetails.loadBankAccountlist();
       });
    });
  },

  viewBankAccountDetails: function() {
    $(".bank-account-details").on('click', function() {
      var bankAccountDetails = $(this).closest('td').find('.institute-bank-account-info').text().trim();
      var bankAccount = JSON.parse(bankAccountDetails);
      manageInstituteBankAccountDetails.populateBankAccountDetails(bankAccount);
      $("#view-bank-account-details-modal").modal('toggle');
    });
  },

  populateBankAccountDetails: function(bankAccount) {

    manageInstituteBankAccountDetails.resetBankAccountFields();
    $('#view\\.bank-name').text(bankAccount.bankName);
    $('#view\\.bank-branch-name').text(bankAccount.branchName);
    $('#view\\.account-number').text(bankAccount.accountNumber);
    $('#view\\.account-type').text(bankAccount.accountType);
    $('#view\\.account-holder-name').text(bankAccount.accountHolderName);
    $('#view\\.ifsc-code').text(bankAccount.ifscCode);
    $('#view\\.created-at').text(getFormattedDate(bankAccount.createdAt));
    $('#view\\.is-primary').text(bankAccount.primary ? 'Yes' : 'No');
    $('#view\\.is-active').text(bankAccount.status == "ACTIVE" ? 'Yes' : 'No');
    $('#view\\.account-id').text(bankAccount.accountId);
},

resetBankAccountFields : function(){
  $('#view\\.bank-name-name').text('');
  $('#view\\.bank-branch-name').text('');
  $('#view\\.account-number').text('');
  $('#view\\.account-type').text('');
  $('#view\\.account-holder-name').text('');
  $('#view\\.ifsc-code').text('');
  $('#view\\.is-primary').text('');
  $('#view\\.account-id').val('');
  $('#vview\\.is-active').val('');
  $('#view\\.created-at').val('');
  },

  updateBankAccountDetails: function(ref) {
    $(".update-bank-account").on('click', function() {
      var bankAccountDetails = $(this).closest('td').find('.institute-bank-account-info').text().trim();
      var bankAccount = JSON.parse(bankAccountDetails);
      manageInstituteBankAccountDetails.populateBankAccountDetailsforupdate(bankAccount);
      $("#update-bank-account-modal").modal('toggle');
    });
  },

  populateBankAccountDetailsforupdate: function(bankAccount) {
    $('#update\\.bank-name').val(bankAccount.bankName);
    $('#update\\.bank-branch-name').val(bankAccount.branchName);
    $('#update\\.account-number').val(bankAccount.accountNumber);
    $('#update\\.account-type').val(bankAccount.accountType);
    $('#update\\.account-holder-name').val(bankAccount.accountHolderName);
    $('#update\\.ifsc-code').val(bankAccount.ifscCode);
    $('#update\\.is-primary').prop('checked', bankAccount.primary);
    $('#update\\.is-active').prop('checked', bankAccount.status == "ACTIVE" ? true : false);
    $('#update\\.bank-account-id').val(bankAccount.accountId);
  },

  updateBankAccount: function () {
    var invalid = validations($("#update-bank-account-modal"));
    if (invalid) {
      return;
    }
    var bankName = $("#update\\.bank-name").val();
    var branchName = $("#update\\.bank-branch-name").val();
    var accountNumber = $("#update\\.account-number").val();
    var accountHolderName = $("#update\\.account-holder-name").val();
    var ifscCode = $("#update\\.ifsc-code").val();
    var accountType = $("#update\\.account-type").val() || null;
    var isPrimary = $("#update\\.is-primary").is(":checked");
    var isActive = $("#update\\.is-active").is(":checked")? "ACTIVE": "INACTIVE";
    var accountId = $("#update\\.bank-account-id").val();

    var payload = {"accountId": accountId,"accountType": accountType,"bankName":bankName,"branchName":branchName,"accountHolderName":accountHolderName,"accountNumber":accountNumber,"ifscCode":ifscCode,"status":isActive,"primary":isPrimary};
    $("#update-bank-account-modal").modal("toggle");

    ajaxClient.post("/institute-management/update-bank-account/"+ accountId, {'instituteBankAccountDetailsPayload' : JSON.stringify(payload)}, function (data) {
      $("#bank-account\\.status-modal-container").html(data);
      $("#bank-account\\.status-modal").modal("toggle");
      manageInstituteBankAccountDetails.loadBankAccountlist();
    });
  },

  registerDeleteBankAccountCallBack : function (){
    $('.delete-bank-account').on('click', function () {
      var bankAccountDetails = $(this).closest('td').find('.institute-bank-account-info').text().trim();
      var bankAccount = JSON.parse(bankAccountDetails);
      $('#delete\\.bank-account-id').val(bankAccount.accountId);
      $("#delete-bank-account-info-modal").modal('toggle');
     });
  },

  deleteBankAccount : function (){
    $("#delete-bank-account-info-modal").modal('toggle');
    var accountId = $("#delete\\.bank-account-id").val();
    ajaxClient.post("/institute-management/delete-bank-account/"+accountId,{}, function(data){
        $("#bank-account\\.status-modal-container").html(data);
        $("#bank-account\\.status-modal").modal('toggle');
        manageInstituteBankAccountDetails.loadBankAccountlist();
      });
  },
}

// ----------------------------------------- Manage Institute Details ------------------------------ //

var manageInstituteDetails = {

 dataCache : {},

  loadManageInstituteDetailsPage : function(data){
        ajaxClient.get("/institute-management/manage-institute-details-home", function(data) {
        $("#main-content").html(data);
         manageInstituteDetails.initPage()
    });
 },

  initPage : function(){
   manageInstituteDetails.registerUpdateInstituteDetailsModal()
  },


  registerUpdateInstituteDetailsModal : function(){
     $("#update-institute-details").on('click',function(){
     var instituteName = $("#institute-name").val();
     var branchName = $("#branch-name").val();
     var addressLine1 = $("#address-line-1").val();
     var addressLine2 = $("#address-line-2").val();
     var city = $("#city").val();
     var state = $("#state").val();
     var country = $("#country").val();
     var zipcode = $("#pin-code").val();
     var landmark = $("#landmark").val();
     var email = $("#email").val();
     var phoneNumber = $("#phone-number").val();

     var letterHeadLine1 = $("#letter-head-1").val();
     var letterHeadLine2 = $("#letter-head-2").val();
     var instituteMetadataVariablesMap = {};
     var affiliationNumber = $("#affiliation-no").val();
     var affiliationNumber = $("#affiliation-no").val();
     if (manageInstituteDetails.isNotNullOrEmpty(affiliationNumber)) {
         instituteMetadataVariablesMap["AFFILIATION_NUMBER"] = affiliationNumber;
      }

      var diseCode = $("#dise-code").val();
      if (manageInstituteDetails.isNotNullOrEmpty(diseCode)) {
          instituteMetadataVariablesMap["DISE_CODE"] = diseCode;
      }

      var schoolCode = $("#school-code").val();
      if (manageInstituteDetails.isNotNullOrEmpty(schoolCode)) {
          instituteMetadataVariablesMap["SCHOOL_CODE"] = schoolCode;
      }

      var priAffiliationNumber = $("#pri-edu-affli-no").val();
      if (manageInstituteDetails.isNotNullOrEmpty(priAffiliationNumber)) {
          instituteMetadataVariablesMap["PRIMARY_EDUCATION_AFFILIATION_NUMBER"] = priAffiliationNumber;
      }

      var rteAffiliationNumber = $("#rte-affli-no").val();
      if (manageInstituteDetails.isNotNullOrEmpty(rteAffiliationNumber)) {
          instituteMetadataVariablesMap["RTE_AFFILIATION_NUMBER"] = rteAffiliationNumber;
      }
      var institutePayload = { 'instituteName': instituteName, 'branchName': branchName, 'letterHeadLine1': letterHeadLine1, 'letterHeadLine2': letterHeadLine2, 'addressLine1': addressLine1,
                        'addressLine2': addressLine2, 'city': city, 'state': state, 'country': country, 'zipcode': zipcode, 'landmark': landmark, 'email': email, 'phoneNumber': phoneNumber, 'instituteMetadataVariablesMap' : instituteMetadataVariablesMap};

        ajaxClient.post("/institute-management/update-institute",{'institutePayload' : JSON.stringify(institutePayload)}, function(data) {
        $("#manage-institute-details-status-modal-container").html(data);
        $("#manage-institute-details-status-modal").modal('toggle');
         manageInstituteDetails.loadManageInstituteDetails();
  });
  })

},
loadManageInstituteDetails : function(){
         ajaxClient.get("/institute-management/manage-institute-details", function(data) {
         $("#manage-institute-details").html(data);
    });
 },

isNotNullOrEmpty :  function(value){
     return value !== null && value !== "";
 }

}


// ----------------------------------------- Manage Institute Logo ------------------------------ //


var manageInstituteLogo = {

 dataCache : {},

  loadManageInstituteLogoPage : function(data){
       ajaxClient.get("/institute-management/manage-institute-logo-home", function(data) {
       $("#main-content").html(data);
       manageInstituteLogo.populateUploadedDocuments();
    });
 },

   loadManageInstituteLogo : function(){
       ajaxClient.get("/institute-management/manage-institute-logo", function(data) {
       $("#manage-institute-uploaded-logo").html(data);
       manageInstituteLogo.populateUploadedDocuments();

     });
  },
   resetNewDocumentUploadPopup :   function() {
      $("#upload-document-type").val("");
      $("#upload-document-file").val("");
      $("#upload-document-file-label").text("");
      $("#upload-document-name").val("");
      registerUploadFileCallback();
  },

   populateUploadedDocuments: function() {
      var instituteDocumentJson = $("#upload-documents").text().trim();
      if(instituteDocumentJson === 'None' || instituteDocumentJson === "" || instituteDocumentJson === null || instituteDocumentJson === undefined) {
          $("#uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>");
           return;
      }
      var instituteDocumentList = JSON.parse(instituteDocumentJson);
      if(instituteDocumentList == null || instituteDocumentList.length == 0){
            $("#uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>");
           return;
      }
      var documentsList = "<br>";
      var itemsPerRow = 3;
      var numberOfDocument = 0;
      for(var i = 0 ; i < instituteDocumentList.length; i++){
          var instituteDocument = instituteDocumentList[i];
          if(instituteDocument.documentId == null) {
            continue;
          }
          if(numberOfDocument % 3 == 0){
              if(numberOfDocument != 0){
                  documentsList += "</div>";
              }
              documentsList += "<div class=\"row\">";
          }
          numberOfDocument++;
          var uplaodTimeText = "Uploaded on : " + getFormattedDate(instituteDocument.uploadTime);
          documentsList += "<div class=\"col-sm-4\"> <div class=\"card bg-light text-center\"> <div class=\"card-header\"> <h5> <strong> "+ instituteDocument.documentName + " </strong></h5> </div> <div class=\"card-body\"> <p style=\"display:none;\" class=\"view-document-id\"> "+ instituteDocument.documentId + " </p> <p class=\"card-text\"> Category : "+ instituteDocument.documentTypeDisplayName+" </p> <a href=\"#\" class=\"btn btn-outline-info download-document\">Download </a> <a href=\"#\" class=\"btn btn-outline-danger delete-document\">Delete </a> </div> <div class=\"card-footer text-muted\"> "+ uplaodTimeText + " </div> </div> </div>"
        }
        documentsList += "</div> <br>";
        $("#uploaded-documents").html(documentsList);
        manageInstituteLogo.bindDocumentActions();
    },

     bindDocumentActions : function() {
      $('.download-document').on('click', function () {
          var documentId = $(this).parent().find('p.view-document-id').text().trim();
          window.open(baseURL+"/institute-management/document-download/"+documentId, '_blank');
      });

      $('.delete-document').on('click', function () {
          var documentId = $(this).parent().find('p.view-document-id').text().trim();
          $("#document-delete-confirm-button").attr("onclick","manageInstituteLogo.deleteDocument('"+documentId+"')");
          $("#document-delete-confirm-modal").modal({backdrop: 'static', keyboard: false});
      });
    },

     uploadDocument: async function() {
        var documentType = $("#upload-document-type option:selected").val().trim();
        if(documentType == ""){
          showErrorDialogBox("Document type field is mandatory please fill it then proceed.");
          return;
        }

        var file ;
        var ONE_KB = 1024;
        var FILE_SIZE_LIMIT = 50;
        if (($("#upload-document-file"))[0].files.length > 0) {
            var uncompressedFile = ($("#upload-document-file"))[0].files[0];
            file = await compressFileUtils.compress(uncompressedFile);
            if((file.size / ONE_KB) > FILE_SIZE_LIMIT){
              showErrorDialogBox("Size Of document cannot be greater than " + FILE_SIZE_LIMIT + " kb");
              return;
            }
        } else {
            showErrorDialogBox("No file selected. Please choose a document to upload");
            return;
        }
        var documentName = "";
        var formData = new FormData();
        formData.append('document', file);
        formData.append('documentType', documentType);
        formData.append('documentName', documentName);
        $("#upload-new-document-modal").modal("toggle");
        ajaxClient.uploadFile("/institute-management/document-upload", formData, function(data){
            $("#manage-document-upload-status-modal-container").html(data);
            $("#manage-document_upload_status-modal").modal({backdrop: 'static', keyboard: false});
             manageInstituteLogo.loadManageInstituteLogo();

        });
    },
    deleteDocument: function(documentId) {
          ajaxClient.post("/institute-management/document-delete/"+documentId,{},function(data){
          $("#manage-document-upload-status-modal-container").html(data);
          $("#manage-document_upload_status-modal").modal({backdrop: 'static', keyboard: false});
          manageInstituteLogo.loadManageInstituteLogo();
        });
    }
 }


 var documentHandler = {

  dataCache : {},

  registerUploadDocumentCallBack : function () {
    $('.upload-standard-document').on('click', function () {
      var standardSessionInfoJson = $(this).parent().find('.manage-standard-session-data-json').text().trim();
      var standardSessionInfo = JSON.parse(standardSessionInfoJson);
      $("#upload-document-standard-id").text(standardSessionInfo.standardRowDetails.standardId);
      $("#upload-document-section-id").text(standardSessionInfo.standardRowDetails.sectionId);
      $('#manage-class-teachers-list').attr('style','display:none');
      $('#academic-year-display').attr('style','display:none');
      $('#session-dropdown').attr('style','display:none');
      $('#standard-session-upload-document-screen').attr('style','display:block');
      $("#upload-document-standard-name").text(standardSessionInfo.standardRowDetails.displayNameWithSection);
      documentHandler.populateUploadedDocuments(standardSessionInfo.standardSessionDocuments);
    });
    registerUploadFileCallback();
  },

  populateUploadedDocuments : function (standardSessionDocuments) {
    if(standardSessionDocuments == null || standardSessionDocuments.length == 0){
      $("#standard-uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>");
      return;
    }
    var documentsList = "<br>";
    var itemsPerRow = 3;
    var numberOfDocument = 0;
    for(var i = 0 ; i < standardSessionDocuments.length; i++){
      var standardDocument = standardSessionDocuments[i];
      if(standardDocument.documentId == null) {
        continue;
      }
      if(numberOfDocument % 3 == 0){
          if(numberOfDocument != 0){
              documentsList += "</div>";
          }
          documentsList += "<div class=\"row\">";
      }
      numberOfDocument++;
      var uplaodTimeText = "Uploaded on : " + getFormattedDate(standardDocument.uploadTime);
      documentsList += "<div class=\"col-sm-4\"> <div class=\"card bg-light text-center\"> <div class=\"card-header\"> <h5> <strong> "+ standardDocument.documentName + " </strong></h5> </div> <div class=\"card-body\"> <p style=\"display:none;\" class=\"view-document-id\"> "+ standardDocument.documentId + " </p> <a href=\"#\" class=\"btn btn-outline-info download-standard-document\">Download </a> <a href=\"#\" class=\"btn btn-outline-danger delete-standard-document\">Delete </a> </div> <div class=\"card-footer text-muted\"> "+ uplaodTimeText + " </div> </div> </div>"
    }
    documentsList += "</div> <br>";
    $("#standard-uploaded-documents").html(documentsList);
    documentHandler.bindStandardDocumentActions();
  },

  returnToMainScreen : function () {
    manageClassTeachers.loadManageClassTeacherslist();
      $('#manage-class-teachers-list').attr('style','display:block');
      $('#academic-year-display').attr('style','display:block');
      $('#session-dropdown').attr('style','display:block');
      $('#standard-session-upload-document-screen').attr('style','display:none');
  },

  resetNewDocumentUploadPopup : function () {
    $("#upload-document-file").val("");
    $("#upload-document-file-label").text("");
    $("#upload-standard-document-name").val("");
  },

  bindStandardDocumentActions : function () {
    $('.download-standard-document').on('click', function () {
        var academicSessionId = academicSessionHandler.getSelectedSessionId();
        var standardId = $("#upload-document-standard-id").text().trim();
        var sectionId = $("#upload-document-section-id").text().trim();
        var documentId = $(this).parent().find('p.view-document-id').text().trim();
        window.open(baseURL+"/institute-management/standard-document-download/"+standardId+"/"+documentId+"?academic_session_id="+academicSessionId+"&section_id="+sectionId, '_blank');
    });

    $('.delete-standard-document').on('click', function () {
        var documentId = $(this).parent().find('p.view-document-id').text().trim();
        $("#standard-document-delete-confirm-button").attr("onclick","documentHandler.deleteStandardDocument('"+documentId+"')");
        $("#standard-document-delete-confirm-modal").modal({backdrop: 'static', keyboard: false});
    });
  },

  deleteStandardDocument : function (documentId) {
      var academicSessionId = academicSessionHandler.getSelectedSessionId();
      var standardId = $("#upload-document-standard-id").text().trim();
      var sectionId = $("#upload-document-section-id").text().trim();
      ajaxClient.post("/institute-management/standard-document-delete/"+standardId+"/"+documentId+"?academic_session_id="+academicSessionId+"&section_id="+sectionId,{},function(data){
        $("#standard-document-status-modal-container").html(data);
        $("#standard-document-delete-status-modal").modal({backdrop: 'static', keyboard: false});
        var responseStatus = JSON.parse($("#success-document-delete-response-status").text().trim());
        if(responseStatus){
          var standardDocumentsJson = $("#success-document-delete-response").text().trim();
          var standardDocuments = JSON.parse(standardDocumentsJson);
          documentHandler.populateUploadedDocuments(standardDocuments);
         }
      });
  },

  uploadStandardDocument : async function() {
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    var standardId = $("#upload-document-standard-id").text();
    var sectionId = $("#upload-document-section-id").text();
    var file ;
    var ONE_KB = 1024;
    var FILE_SIZE_LIMIT = 500;
    if (($("#upload-document-file"))[0].files.length > 0) {
        var uncompressedFile = ($("#upload-document-file"))[0].files[0];
        file = await compressFileUtils.compress(uncompressedFile);
        if((file.size / ONE_KB) > FILE_SIZE_LIMIT){
          showErrorDialogBoxWithExistingModalDetails("File size exceeds " + FILE_SIZE_LIMIT + " KB after compression. Please reduce the file size and try uploading again.", "#upload-new-document-modal");
          return;
        }
    } else {
        showErrorDialogBoxWithExistingModalDetails("No file selected. Please choose a document to upload", "#upload-new-document-modal");
        return;
    }

    var documentName = $("#upload-standard-document-name").val();
    if(documentName == "") {
      showErrorDialogBoxWithExistingModalDetails("Document name field is mandatory please fill it then proceed", "#upload-new-document-modal");
      return;
    }
    var formData = new FormData();
    formData.append('document', file);
    formData.append('documentName', documentName);
    formData.append('academicSessionId', academicSessionId);
    formData.append('standardId', standardId);
    formData.append('sectionId', sectionId);
    $("#upload-new-document-modal").modal("toggle");
    ajaxClient.uploadFile("/institute-management/standard-document-upload", formData, function(data){
        $("#standard-document-status-modal-container").html(data);
        $("#standard-document-upload-status-modal").modal({backdrop: 'static', keyboard: false});
        var responseStatus = JSON.parse($("#success-document-upload-response-status").text().trim());
        if(responseStatus){
          var standardDocumentsJson = $("#success-document-upload-response").text().trim();
          var standardDocuments = JSON.parse(standardDocumentsJson);
          documentHandler.populateUploadedDocuments(standardDocuments);
         }
    });
  },

 }

// ----------------------------------------- Manage Standards ------------------------------ //
var manageStandards = {

  dataCache : {},

  openEditStandardsModal : function(){
    manageStandards.refreshStandardsData();
    var standardsData = readJson('#edit-standards-data');
    manageStandards.dataCache.standards = standardsData;
    manageStandards.dataCache.count = 0;

    var academicYear = academicSessionHandler.getSelectedSession();
    $("#edit-standards-session-display").val(academicYear.displayName);

    manageStandards.populateStandardsTable();
    $("#edit-standards-modal").modal('toggle');
  },

  populateStandardsTable : function(){
    var standardsTableContent = "";
    var standards = manageStandards.dataCache.standards;

    for(var i = 0; i < standards.length; i++){
      manageStandards.dataCache.count += 1;
      var standard = standards[i];
      var standardRow = manageStandards.getStandardRow(standard, false);
      standardsTableContent += standardRow;
    }

    standardsTableContent += "<tr id=\"edit-standards-add-row-bottom\" style=\"display:none;\"></tr>";
    $("#edit-standards-input-div").html(standardsTableContent);
  },

  getStandardRow : function(standard, isNew){
    var rowId = "standard-row-" + manageStandards.dataCache.count;
    var standardId = standard ? standard.standardId : "";
    var standardName = standard ? standard.standardName : "";
    var selectedStream = standard ? standard.stream : "";
    var level = standard ? standard.level : "";

    var streamOptions = manageStandards.getStreamOptions(selectedStream);
    var insertButton = "<button type='button' class='btn btn-sm btn-outline-primary rounded-circle' style='width:24px; height:24px; padding:0; font-size:14px;' title='Insert Class' onclick='manageStandards.insertStandardRow(this)'>+</button>";

    var deleteButton = isNew ?
    "<button type='button' class='btn btn-sm btn-outline-danger rounded-circle' style='width:24px; height:24px; padding:0; font-size:16px;' title='Delete Class' onclick='manageStandards.deleteStandardRow(this)'>&times;</button>" :
    "";

    var standardRow = "<tr class=\"" + (isNew ? "new-standard-row" : "existing-standard-row") + "\" id=\"" + rowId + "\">" +
      "<td style=\"vertical-align: middle; text-align: center; white-space: nowrap;\">" + insertButton + "</td>" +
      "<td>" +
        "<input type=\"hidden\" class=\"standard-id\" value=\"" + standardId + "\">" +
        "<input type=\"text\" class=\"form-control standard-name mandatory-field\" placeholder=\"Standard Name...\" value=\"" + standardName + "\" onchange=\"manageStandards.validateDuplicateStandardStream(this)\">" +
      "</td>" +
      "<td>" +
        "<select class=\"form-control standard-stream mandatory-field\" onchange=\"manageStandards.validateDuplicateStandardStream(this)\">" +
          streamOptions +
        "</select>" +
      "</td>" +
      "<td>" +
        "<input type=\"number\" class=\"form-control standard-level mandatory-field\" placeholder=\"Level...\" value=\"" + level + "\" onchange=\"manageStandards.validateDuplicateLevel(this)\">" +
      "</td>" +
      "<td style=\"vertical-align: middle; text-align: center; white-space: nowrap;\">" + deleteButton + "</td>" +
    "</tr>";

    return standardRow;
},


insertStandardRow: function(ref){
    manageStandards.dataCache.count += 1;
    var newRow = manageStandards.getStandardRow(null, true);
    $(ref).closest('tr').after(newRow);
    manageStandards.adjustLevels();
},
adjustLevels: function(){
    var levelCounter = 1;
    $("#edit-standards-input-div tr").not("#edit-standards-add-row-bottom").each(function(){
        $(this).find('.standard-level').val(levelCounter);
        levelCounter++;
    });
},

  getStreamOptions : function(selectedStream){
    var streams = [
      {value: "SCIENCE", display: "Science"},
      {value: "COMMERCE", display: "Commerce"},
      {value: "ARTS", display: "Arts"},
      {value: "AGRICULTURE", display: "Agriculture"},
      {value: "MATHS", display: "Maths"},
      {value: "BIOLOGY", display: "Biology"},
      {value: "HUMANITIES", display: "Humanities"},
      {value: "HINDI", display: "Hindi"},
      {value: "ENGLISH", display: "English"},
      {value: "PCB", display: "PCB"},
      {value: "PCM", display: "PCM"},
      {value: "JEE", display: "JEE"},
      {value: "NEET", display: "NEET"},
      {value: "NDA", display: "NDA"},
      {value: "NA", display: "NA"}
    ];

    var options = "<option value=\"\">Select Stream</option>";
    for(var i = 0; i < streams.length; i++){
      var selected = (streams[i].value === selectedStream) ? "selected" : "";
      options += "<option value=\"" + streams[i].value + "\" " + selected + ">" + streams[i].display + "</option>";
    }

    return options;
  },
// addNewStandardRow: function(){
//     var bottomRowId = "edit-standards-add-row-bottom";
//     manageStandards.dataCache.count += 1;
//     var newRow = manageStandards.getStandardRow(null, true);
//     $("#" + bottomRowId).before(newRow);
//     manageStandards.adjustLevels(); 
// },

deleteStandardRow: function(ref){
    $(ref).closest('tr').remove();
    manageStandards.adjustLevels(); 
},

  validateDuplicateStandardStream : function(element){
    var currentRow = $(element).closest('tr');
    var currentStandardName = currentRow.find('.standard-name').val().trim();
    var currentStream = currentRow.find('.standard-stream').val().trim();

    if(currentStandardName === "" || currentStream === ""){
      return;
    }

    var duplicateFound = false;
    $("#edit-standards-input-div tr").not(currentRow).each(function(){
      var standardName = $(this).find('.standard-name').val();
      var stream = $(this).find('.standard-stream').val();

      if(standardName && stream && standardName.trim() === currentStandardName && stream.trim() === currentStream){
        duplicateFound = true;
        return false;
      }
    });

    if(duplicateFound){
      alert("Duplicate Standard Name + Stream combination is not allowed");
      $(element).val("");
      return;
    }
  },

  validateDuplicateLevel : function(element){
    var currentRow = $(element).closest('tr');
    var currentLevel = $(element).val().trim();

    if(currentLevel === ""){
      return;
    }

    var duplicateFound = false;
    $("#edit-standards-input-div tr").not(currentRow).each(function(){
      var level = $(this).find('.standard-level').val();

      if(level && level.trim() === currentLevel){
        duplicateFound = true;
        return false;
      }
    });

    if(duplicateFound){
      alert("Duplicate Level is not allowed");
      $(element).val("");
      return;
    }
  },

  updateStandards : function(){
    var invalid = validateMandatoryFields($("#edit-standards-modal"));

    if(invalid) {
      alert("Please fill all mandatory fields");
      return;
    }

    var standardsList = [];
    $("#edit-standards-input-div tr").not("#edit-standards-add-row-bottom").each(function(){
      var standardId = $(this).find('.standard-id').val().trim();
      var standardName = $(this).find('.standard-name').val().trim();
      var stream = $(this).find('.standard-stream').val().trim();
      var level = $(this).find('.standard-level').val().trim();

      if(standardName && stream && level){
        var standardData = {
          'standardId': standardId === "" ? null : standardId,
          'standardName': standardName,
          'stream': stream,
          'level': parseInt(level)
        };
        standardsList.push(standardData);
      }
    });

    if(standardsList.length === 0){
      alert("Please add at least one standard");
      return;
    }

    $("#edit-standards-modal").modal('toggle');
    var academicSessionId = academicSessionHandler.getSelectedSessionId();

    ajaxClient.post("/institute-management/update-standards/" + academicSessionId,
      {'standardsData': JSON.stringify(standardsList)},
      function(data){
        $("#manage-sections\\.status-modal-container").html(data);
        $("#manage-sections\\.status-modal").modal({backdrop: 'static', keyboard: false});
        manageSections.loadManageSectionslist();
        manageStandards.refreshStandardsData();
      }
    );
  },

  refreshStandardsData : function(){
    var academicSessionId = academicSessionHandler.getSelectedSessionId();
    ajaxClient.get("/institute-management/get-standards-json/" + academicSessionId, function(data) {
        // data is already JSON from the new endpoint
        var updatedStandardsData = data;

        console.log("Updated standards data:", updatedStandardsData);
        if(updatedStandardsData) {
          // Update the hidden standards data in the current page
          $('#edit-standards-data').text(JSON.stringify(updatedStandardsData));

          // Update the cache with fresh data
          manageStandards.dataCache.standards = updatedStandardsData;
          manageStandards.dataCache.count = 0;
        }
    });
  }

};