$(document).ready(function(){menuLoader.registerSidebarMenu(),homePage.initHomePage(),paymentReminder.readPaymentState()});var menuLoader={registerSidebarMenu:function(){sideBarHoverEventCallback(),activateMenuItem(),menuLoader.registerHomeMenu(),menuLoader.registerManageSectionsMenu(),menuLoader.registerManageClassTeachersMenu(),menuLoader.registerManageInstituteBankDetailsNav(),menuLoader.registerManageInstituteDetailsNav(),menuLoader.registerManageInstituteLogoNav()},registerHomeMenu:function(){$("#homeNav").on("click",function(){homePage.loadHomePage()})},registerManageSectionsMenu:function(){$("#manageSectionsNav").on("click",function(){manageSections.loadManageSectionsPage()})},registerManageClassTeachersMenu:function(){$("#manageClassTeachersNav").on("click",function(){manageClassTeachers.loadManageClassTeachersPage()})},registerManageInstituteBankDetailsNav:function(){$("#manageInstituteBankDetailsNav").on("click",function(){manageInstituteBankAccountDetails.loadManageInstituteBankAccounntDetailsPage()})},registerManageInstituteDetailsNav:function(){$("#manageInstituteDetailsNav").on("click",function(){manageInstituteDetails.loadManageInstituteDetailsPage()})},registerManageInstituteLogoNav:function(){$("#manageInstituteLogoNav").on("click",function(){manageInstituteLogo.loadManageInstituteLogoPage()})}},homePage={initHomePage:function(){academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)},loadHomePage:function(){ajaxClient.get("/institute-management/home",function(e){$("#main-content").html(e),academicSessionHandler.bindSessionChangeEvent(homePage.loadHomePageForSession)})},loadHomePageForSession:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/institute-management/session-home/"+e,function(e){$("#institute-management-dashboard-session-content").html(e)})},refreshHomePage:function(){homePage.loadHomePageForSession()}},manageSections={dataCache:{},loadManageSectionsPage:function(){ajaxClient.get("/institute-management/manage-sections-home",function(e){$("#main-content").html(e),manageSections.initPage(),academicSessionHandler.bindSessionChangeEvent(manageSections.changeSession)})},initPage:function(){manageSections.registerUpdateSectionsModal(),manageSections.registerAddSectionsModal(),manageSections.registerDeleteSectionsModal()},clearCache:function(){manageSections.dataCache={}},changeSession:function(){manageSections.loadManageSectionslist()},loadManageSectionslist:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/institute-management/manage-sections/"+e,function(e){$("#manage-sections-list").html(e),manageSections.initPage(),manageSections.clearCache()})},registerUpdateSectionsModal:function(){$(".update-manage-sections").on("click",function(){var e=$(this).parent().find(".manage-sections-info").text().trim(),a=JSON.parse(e);manageSections.dataCache.sectionDetails=a,$("#update\\.standard-id").val(a.standardId);for(var t="",n=0;n<a.standardSectionList.length;n++){var i=a.standardSectionList[n].sectionName;html='<div class="form-group  col-md-8"><input pattern="[A-Z]{1}" type="text" class="form-control update-rename-manage-sections add-label-input mandatory-field update.section-name" placeholder="Enter Section Name"   value='+i+"></div>",t+=html}$("#update\\.standard-sections-id").html(t),$("#update-manage-sections-modal").modal("toggle")})},registerAddSectionsModal:function(){$(".add-manage-sections").on("click",function(){var e=$(this).parent().find(".manage-sections-info").text().trim(),a=JSON.parse(e),t=academicSessionHandler.getSelectedSessionId(),n=a.standardId;$("#add\\.standard-id").val(n),ajaxClient.get("/institute-management/class-students/"+t+"?standardId="+n,function(e){$("#manage-sections-list").html(e);var t=$("#class-students").text(),n="";if("None"!=t&&0!=a.standardSectionList.length){for(var i=0;i<a.standardSectionList.length;i++){var s=a.standardSectionList[i].sectionName;html='<div style="display:none;"><p style="display:none;" id="include-radio">false</p><div class="form-group row sections-remove col-md-10"><input id="sectionInput" pattern="[A-Z]{1}" type="text" class="form-control col-md-9 hidden-rename-manage-sections add-label-input mandatory-field add.section-name" placeholder="Enter Section Name"  value='+s+" readonly></div></div>",n+=html}html='<p style="display:none;" id="include-radio">false</p><div class="form-group row sections-remove col-md-8"><input id="sectionInput" pattern="[A-Z]{1}" type="text" class="form-control col-md-9 add-rename-manage-sections add-label-input mandatory-field add.section-name" placeholder="Enter Section Name"></div>',n+=html}else"None"!=t&&0==a.standardSectionList.length?(html='<p style="display:none;" id="include-radio">true</p><div class="form-group row sections-remove col-md-12"><input id="sectionInput" pattern="[A-Z]{1}" type="text" class="form-control add-rename-manage-sections col-md-6 add-label-input mandatory-field add.section-name" placeholder="Enter Section Name" ><div class="mt-2"><input type="radio" name="standard" class="ml-0 pt-2 standard mt-0 mandatory-field"><p style="display:inline-block" class="mb-0 pl-1">Move Existing Students to this section</p></div></div>',n+=html):"None"!=t||0==a.standardSectionList.length&&0!=a.standardSectionList.length||(html='<p style="display:none;" id="include-radio">false</p><div class="form-group row sections-remove col-md-8"><input id="sectionInput" pattern="[A-Z]{1}" type="text" class="form-control add-rename-manage-sections col-md-9 add-label-input mandatory-field add.section-name" placeholder="Enter Section Name" ></div>',n+=html);$("#add\\.standard-sections-id").html(n),$("#add-manage-sections-modal").modal("toggle"),manageSections.initPage(),manageSections.clearCache()})})},addMoreClass:function(e){"true"==$("#include-radio").html()?(html=$("#add\\.standard-sections-id").html(),newHtml='<div class="form-group row sections-remove col-md-12"><input id="sectionInput" pattern="[A-Z]{1}" type="text" class="form-control add-rename-manage-sections col-md-6 mt-1 add-label-input mandatory-field add.section-name" placeholder="Enter Section Name" ><button type="button" class="col-md-2 mt-1 close" data-dismiss="modal" aria-label="Close" onclick="manageSections.removeSectionDetailsRow(this);"><span aria-hidden="true"> &times; </span></button><div class="mt-2"><input type="radio" name="standard" class="ml-0 standard mt-0 pt-2 mandatory-field"><p style="display:inline-block" class="pl-1">Move Existing Students to this section</p></div></div>',$("#add\\.standard-sections-id").append(newHtml)):(html=$("#add\\.standard-sections-id").html(),newHtml='<p style="display:none;" id="include-radio">false</p><div class="form-group row sections-remove col-md-8"><input id="sectionInput" pattern="[A-Z]{1}" type="text" class="form-control col-md-9 add-rename-manage-sections add-label-input mandatory-field add.section-name" placeholder="Enter Section Name"  value=""><button type="button" class="col-md-2 close" data-dismiss="modal" aria-label="Close" onclick="manageSections.removeSectionDetailsRow(this)"><span aria-hidden="true"> × </span></button></div>',$("#add\\.standard-sections-id").append(newHtml))},removeSectionDetailsRow:function(e){$(e).parent().remove()},addSectionsDetails:function(){if(!validations($("#add-manage-sections-modal"))){var e=$("#include-radio").html(),a=getAddSectionsName(),t=Array.from(new Set(a)),n=getHiddenSectionNames();if(a.length!=t.length)return $("#add-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Duplicate Section Names");if("false"==e){for(var i=commonBetweenTwoNames(a,n),s="",d=0;d<n.length;d++)s+=n[d],n.length>d+1&&(s+=",");if(0!=i.length)return $("#add-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Section "+s+" are already present.Please write a different section names")}var o=[],l=$("input.standard:checked").parent().parent().find(".add-rename-manage-sections").val();if("true"==e&&(""==l||null==l))return $("#add-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Please Select the section you want the students to move");var c=academicSessionHandler.getSelectedSessionId(),r=$("#add\\.standard-id").val();for(d=0;d<a.length;d++)section_dict={sectionId:null,sectionName:a[d],studentCount:null},o.push(section_dict);$("#add-manage-sections-modal").modal("toggle");var m={instituteId:null,academicSessionId:c,standardId:r,standardSectionsList:o,moveStudentToSection:l};ajaxClient.post("/institute-management/bulk-add-students-section/"+c,{bulkAddStudentSections:JSON.stringify(m)},function(e){$("#manage-sections\\.status-modal-container").html(e),$("#manage-sections\\.status-modal").modal("toggle"),manageSections.loadManageSectionslist()})}},registerDeleteSectionsModal:function(){$(".delete-manage-sections").on("click",function(){var e=$(this).parent().find(".manage-sections-info").text().trim(),a=JSON.parse(e),t=(academicSessionHandler.getSelectedSessionId(),a.standardId);$("#delete\\.standard-id").val(t);for(var n="",i=0;i<a.standardSectionList.length;i++){var s=a.standardSectionList[i].sectionName,d=a.standardSectionList[i].sectionId;html='<p style="display:none;" id="include-radio">false</p><div class="form-group row sections-remove col-md-8"><input type="checkbox" class="section-id" name="" value=""><p class="bulk-section-id" style="display:none;">'+d+'</p><input id="sectionInput" pattern="[A-Z]{1}" type="text" class="form-control ml-2 delete-rename-manage-sections add-label-input mandatory-field delete.section-name col-md-8" placeholder="Enter Section Name"  value='+s+" readonly></div>",n+=html}$("#delete\\.standard-sections-id").html(n),$("#delete-manage-sections-modal").modal("toggle"),manageSections.initPage(),manageSections.clearCache()})},deleteSectionDetails:function(){if(!validations($("#delete-manage-sections-modal"))){var e=manageSections.getDictionarySectionInfos(),a=academicSessionHandler.getSelectedSessionId(),t=$("#delete\\.standard-id").val();if(options="",checkedSection=e[0],unCheckedSection=e[1],0==checkedSection.length)return $("#delete-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Please Select atleast one section");if(0!=unCheckedSection.length){html1="";for(var n=0;n<unCheckedSection.length;n++)html1="<option value="+unCheckedSection[n].sectionId+">"+unCheckedSection[n].sectionName+"</option>",options+=html1;mainhtml='<p>Where do you wish to move them?</p><select class="col-md-6 form-control which-section mandatory-field" name="">'+options+"</select>"}else mainhtml="<p>Do you wish to move the students to this Standard?</p>";$("#delete-manage-sections-modal").modal("toggle");var i=[],s=[];for(n=0;n<checkedSection.length;n++)sectionId=checkedSection[n].sectionId,sectionName=checkedSection[n].sectionName,i.push(sectionId),s.push(sectionName);i=i.join(","),manageSections.dataCache.sectionIds=i,ajaxClient.get("/institute-management/class-students-sections-list/"+a+"?standardId="+t+"&sectionIds="+i,function(e){$("#manage-sections-list").html(e);var a=JSON.parse($("#class-students").html());0==a.length?html='<p style="display:none;" class="std-id">'+t+"</p>Do you wish to delete the section names?":html='<p style="display:none;" class="std-id">'+t+"</p><p>You have selected section "+s.join(", ")+" to be deleted</p><p>"+String(a.length)+" students are present in the selected section to be deleted.</p>"+mainhtml,$("#delete\\.standard-sections-manage-id").html(html),$("#delete-id-manage-sections-modal").modal("toggle"),manageSections.initPage()})}},deleteStudentSectionsDetails:function(){if($("#delete-id-manage-sections-modal").modal("toggle"),!validations($("#delete-id-manage-sections-modal"))){var e=$("select.which-section").val();null!=e&&null!=e||(e=0),section_ids=manageSections.dataCache.sectionIds;var a=academicSessionHandler.getSelectedSessionId(),t=$(".std-id").html();ajaxClient.post("/institute-management/bulk-delete-students-section/"+a+"/"+t+"/"+section_ids+"/"+e,{},function(e){$("#manage-sections\\.status-modal-container").html(e),$("#manage-sections\\.status-modal").modal("toggle"),manageSections.loadManageSectionslist()})}},getDictionarySectionInfos:function(){var e=[],a=[],t=[];return $("input.section-id").each(function(){$(this).is(":checked")?(sectionName=$(this).parent().find("input.delete-rename-manage-sections").val(),sectionId=$(this).parent().find("p.bulk-section-id").first().text().trim(),section_dict={sectionId:sectionId,sectionName:sectionName},t.push(section_dict)):(sectionName=$(this).parent().find("input.delete-rename-manage-sections").val(),sectionId=$(this).parent().find("p.bulk-section-id").first().text().trim(),section_dict={sectionId:sectionId,sectionName:sectionName},a.push(section_dict))}),e.push(t),e.push(a),e},updateSectionsDetails:function(){if(!validations($("#update-manage-sections-modal"))){var e;e=getUpdateSectionsName();var a=Array.from(new Set(e));if(e.length!=a.length)return $("#update-manage-sections-modal").modal("toggle"),void showErrorDialogBox("Duplicate Section Names");for(var t=[],n=academicSessionHandler.getSelectedSessionId(),i=$("#update\\.standard-id").val(),s=manageSections.dataCache.sectionDetails.standardSectionList,d=0;d<s.length;d++)section_info_dict={sectionId:s[d].sectionId,sectionName:e[d],studentCount:s[d].studentCount},t.push(section_info_dict);$("#update-manage-sections-modal").modal("toggle");var o={instituteId:null,academicSessionId:n,standardId:i,standardSectionsList:t};ajaxClient.post("/institute-management/bulk-update-students-section/"+n,{bulkUpdateStudentSections:JSON.stringify(o)},function(e){$("#manage-sections\\.status-modal-container").html(e),$("#manage-sections\\.status-modal").modal("toggle"),manageSections.loadManageSectionslist()})}}},manageClassTeachers={dataCache:{},loadManageClassTeachersPage:function(){ajaxClient.get("/institute-management/manage-class-teachers-home",function(e){$("#main-content").html(e),manageClassTeachers.initPage(),academicSessionHandler.bindSessionChangeEvent(manageClassTeachers.changeSession)})},initPage:function(){manageClassTeachers.registerUpdateClassTeachersModal(),manageClassTeachers.registerDeleteClassTeachersModal(),documentHandler.registerUploadDocumentCallBack()},clearCache:function(){manageClassTeachers.dataCache={}},changeSession:function(){manageClassTeachers.loadManageClassTeacherslist()},loadManageClassTeacherslist:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/institute-management/manage-class-teachers/"+e,function(e){$("#manage-class-teachers-list").html(e),manageClassTeachers.initPage(),manageClassTeachers.clearCache()})},registerUpdateClassTeachersModal:function(){$(".update-manage-class-teachers").on("click",function(){$(".staff-details-dropdown").val("");var e=$(this).parent().find(".manage-standard-session-data-json").text().trim();$("#update-standard-staff-details-json").text(e);var a=JSON.parse(e);if(null!=a&&""!=a&&null!=a){var t=a.staffLite;if(null!=t&&""!=t&&null!=t){var n=t.staffId;$(".staff-details-dropdown").val(n)}}})},updateClassTeachersDetails:function(e){if(!validations($("#update-manage-class-teachers-modal"))){var a=$("#update-standard-staff-details-json").text(),t=JSON.parse(a),n=academicSessionHandler.getSelectedSessionId(),i=t.standardRowDetails.standardId,s=t.standardRowDetails.sectionId;null!=s&&null!=s||(s="");var d=$(e).parent().parent().find(".staff-details-dropdown").val();$("#update-manage-class-teachers-modal").modal("toggle"),ajaxClient.post("/institute-management/update-class-teacher-details/"+n+"/"+i+"/"+d+"?section_id="+s,{},function(e){$("#manage-class-teachers-status-modal-container").html(e),$("#manage-class-teachers-status-modal").modal("toggle"),manageClassTeachers.loadManageClassTeacherslist()})}},registerDeleteClassTeachersModal:function(){$(".delete-manage-class-teachers").on("click",function(){var e=$(this).parent().find(".manage-standard-session-data-json").text().trim();$("#delete-standard-staff-details-json").text(e)})},deleteClassTeachersDetails:function(e){var a=$("#delete-standard-staff-details-json").text(),t=JSON.parse(a),n=academicSessionHandler.getSelectedSessionId(),i=t.standardRowDetails.standardId,s=t.standardRowDetails.sectionId;null!=s&&null!=s||(s="");$(e).parent().parent().find(".staff-details-dropdown").val();$("#delete-manage-class-teachers-modal").modal("toggle"),ajaxClient.post("/institute-management/delete-class-teacher-details/"+n+"/"+i+"?section_id="+s,{},function(e){$("#manage-class-teachers-status-modal-container").html(e),$("#manage-class-teachers-status-modal").modal("toggle"),manageClassTeachers.loadManageClassTeacherslist()})}};function getUpdateSectionsName(){var e=[];return $(".update-rename-manage-sections").each(function(){var a=$(this).val().trim();e.push(a)}),e}function getAddSectionsName(){var e=[];return $(".add-rename-manage-sections").each(function(){var a=$(this).val().trim();e.push(a)}),e}function getHiddenSectionNames(){var e=[];return $(".hidden-rename-manage-sections").each(function(){var a=$(this).val().trim();e.push(a)}),e}function commonBetweenTwoNames(e,a){for(var t=[],n=0;n<e.length;n++)for(var i=0;i<a.length;i++)e[n].trim()==a[i].trim()&&t.push(e[n]);return t}function validations(e){clearValidationErrorDisplay();var a=!1;return $(e).find("input.mandatory-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-field-text"> <span style="color:#e65f76;">This field is mandatory</span></p>'),a=!0)}),$(e).find("select.mandatory-field").each(function(){0!=$(this).find(":selected").length&&""!=$(this).find(":selected").val().trim()||($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-field-text"> <span style="color:#e65f76;">This field is mandatory</span></p>'),a=!0)}),$(e).find("input.mandatory-table-field").each(function(){""==$(this).val()&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="mandatory-table-field-text"> <span style="color:#e65f76;">Mandatory</span></p>'),a=!0)}),$(e).find("input.hour-range").each(function(){($(this).val()<0||$(this).val()>23)&&($(this).css("border","1px solid #ff8795"),a=!0)}),$(e).find("input.minute-range").each(function(){($(this).val()<0||$(this).val()>59)&&($(this).css("border","1px solid #ff8795"),a=!0)}),$(e).find("input.positive-number").each(function(){$(this).val()<0&&($(this).css("border","1px solid #ff8795"),$(this).after('<p class="invalid-input-text"> <span style="color:#e65f76;">Invalid</span></p>'),a=!0)}),a}function clearValidationErrorDisplay(){$(".hour-range").css("border",""),$(".minute-range").css("border",""),$(".positive-number").css("border",""),$(".invalid-input-text").remove(),$(".mandatory-field").css("border",""),$(".mandatory-field-text").remove(),$(".mandatory-table-field").css("border",""),$(".mandatory-table-field-text").remove()}function closeModal(){clearValidationErrorDisplay()}var manageInstituteBankAccountDetails={dataCache:{},loadManageInstituteBankAccounntDetailsPage:function(e){ajaxClient.get("/institute-management/manage-institute-bank-account-details-home",function(e){$("#main-content").html(e),manageInstituteBankAccountDetails.addNewBankAccountCallback(),manageInstituteBankAccountDetails.initPage()})},initPage:function(){manageInstituteBankAccountDetails.viewBankAccountDetails(),manageInstituteBankAccountDetails.updateBankAccountDetails(),manageInstituteBankAccountDetails.registerDeleteBankAccountCallBack(),$("#datatables-reponsive-1").DataTable({paging:!1,searching:!1,columnDefs:[{targets:"no-sort",orderable:!1}],order:[[0,"asc"]]})},resetAddNewBankDetailsModal:function(){$("#add-bank-account-modal").find("input").val("")},loadBankAccountlist:function(){ajaxClient.get("/institute-management/bank-account-details",function(e){$("#bank-account-list").html(e),manageInstituteBankAccountDetails.initPage()})},addNewBankAccountCallback:function(){$("#add-bank-account").on("click",function(){if(!validations($("#add-bank-account-modal"))){var e=$("#bank-name").val().trim(),a=$("#bank-branch-name").val().trim(),t=$("#account-number").val().trim(),n=$("#account-holder-name").val().trim(),i=$("#ifsc-code").val().trim(),s=$("#account-type").val().trim()||null,d=$("#is-primary").is(":checked"),o={accountType:s,bankName:e,branchName:a,accountHolderName:n,accountNumber:t,ifscCode:i,status:$("#is-active").is(":checked")?"ACTIVE":"INACTIVE",primary:d};$("#add-bank-account-modal").modal("toggle"),ajaxClient.post("/institute-management/add-bank-account",{instituteBankAccountDetailsPayload:JSON.stringify(o)},function(e){$("#bank-account\\.status-modal-container").html(e),$("#bank-account\\.status-modal").modal("toggle"),$("#account-type").val(""),manageInstituteBankAccountDetails.loadBankAccountlist()})}})},viewBankAccountDetails:function(){$(".bank-account-details").on("click",function(){var e=$(this).closest("td").find(".institute-bank-account-info").text().trim(),a=JSON.parse(e);manageInstituteBankAccountDetails.populateBankAccountDetails(a),$("#view-bank-account-details-modal").modal("toggle")})},populateBankAccountDetails:function(e){manageInstituteBankAccountDetails.resetBankAccountFields(),$("#view\\.bank-name").text(e.bankName),$("#view\\.bank-branch-name").text(e.branchName),$("#view\\.account-number").text(e.accountNumber),$("#view\\.account-type").text(e.accountType),$("#view\\.account-holder-name").text(e.accountHolderName),$("#view\\.ifsc-code").text(e.ifscCode),$("#view\\.created-at").text(getFormattedDate(e.createdAt)),$("#view\\.is-primary").text(e.primary?"Yes":"No"),$("#view\\.is-active").text("ACTIVE"==e.status?"Yes":"No"),$("#view\\.account-id").text(e.accountId)},resetBankAccountFields:function(){$("#view\\.bank-name-name").text(""),$("#view\\.bank-branch-name").text(""),$("#view\\.account-number").text(""),$("#view\\.account-type").text(""),$("#view\\.account-holder-name").text(""),$("#view\\.ifsc-code").text(""),$("#view\\.is-primary").text(""),$("#view\\.account-id").val(""),$("#vview\\.is-active").val(""),$("#view\\.created-at").val("")},updateBankAccountDetails:function(e){$(".update-bank-account").on("click",function(){var e=$(this).closest("td").find(".institute-bank-account-info").text().trim(),a=JSON.parse(e);manageInstituteBankAccountDetails.populateBankAccountDetailsforupdate(a),$("#update-bank-account-modal").modal("toggle")})},populateBankAccountDetailsforupdate:function(e){$("#update\\.bank-name").val(e.bankName),$("#update\\.bank-branch-name").val(e.branchName),$("#update\\.account-number").val(e.accountNumber),$("#update\\.account-type").val(e.accountType),$("#update\\.account-holder-name").val(e.accountHolderName),$("#update\\.ifsc-code").val(e.ifscCode),$("#update\\.is-primary").prop("checked",e.primary),$("#update\\.is-active").prop("checked","ACTIVE"==e.status),$("#update\\.bank-account-id").val(e.accountId)},updateBankAccount:function(){if(!validations($("#update-bank-account-modal"))){var e=$("#update\\.bank-name").val(),a=$("#update\\.bank-branch-name").val(),t=$("#update\\.account-number").val(),n=$("#update\\.account-holder-name").val(),i=$("#update\\.ifsc-code").val(),s=$("#update\\.account-type").val()||null,d=$("#update\\.is-primary").is(":checked"),o=$("#update\\.is-active").is(":checked")?"ACTIVE":"INACTIVE",l=$("#update\\.bank-account-id").val(),c={accountId:l,accountType:s,bankName:e,branchName:a,accountHolderName:n,accountNumber:t,ifscCode:i,status:o,primary:d};$("#update-bank-account-modal").modal("toggle"),ajaxClient.post("/institute-management/update-bank-account/"+l,{instituteBankAccountDetailsPayload:JSON.stringify(c)},function(e){$("#bank-account\\.status-modal-container").html(e),$("#bank-account\\.status-modal").modal("toggle"),manageInstituteBankAccountDetails.loadBankAccountlist()})}},registerDeleteBankAccountCallBack:function(){$(".delete-bank-account").on("click",function(){var e=$(this).closest("td").find(".institute-bank-account-info").text().trim(),a=JSON.parse(e);$("#delete\\.bank-account-id").val(a.accountId),$("#delete-bank-account-info-modal").modal("toggle")})},deleteBankAccount:function(){$("#delete-bank-account-info-modal").modal("toggle");var e=$("#delete\\.bank-account-id").val();ajaxClient.post("/institute-management/delete-bank-account/"+e,{},function(e){$("#bank-account\\.status-modal-container").html(e),$("#bank-account\\.status-modal").modal("toggle"),manageInstituteBankAccountDetails.loadBankAccountlist()})}},manageInstituteDetails={dataCache:{},loadManageInstituteDetailsPage:function(e){ajaxClient.get("/institute-management/manage-institute-details-home",function(e){$("#main-content").html(e),manageInstituteDetails.initPage()})},initPage:function(){manageInstituteDetails.registerUpdateInstituteDetailsModal()},registerUpdateInstituteDetailsModal:function(){$("#update-institute-details").on("click",function(){var e=$("#institute-name").val(),a=$("#branch-name").val(),t=$("#address-line-1").val(),n=$("#address-line-2").val(),i=$("#city").val(),s=$("#state").val(),d=$("#country").val(),o=$("#pin-code").val(),l=$("#landmark").val(),c=$("#email").val(),r=$("#phone-number").val(),m=$("#letter-head-1").val(),u=$("#letter-head-2").val(),g={},p=$("#affiliation-no").val();p=$("#affiliation-no").val();manageInstituteDetails.isNotNullOrEmpty(p)&&(g.AFFILIATION_NUMBER=p);var f=$("#dise-code").val();manageInstituteDetails.isNotNullOrEmpty(f)&&(g.DISE_CODE=f);var h=$("#school-code").val();manageInstituteDetails.isNotNullOrEmpty(h)&&(g.SCHOOL_CODE=h);var v=$("#pri-edu-affli-no").val();manageInstituteDetails.isNotNullOrEmpty(v)&&(g.PRIMARY_EDUCATION_AFFILIATION_NUMBER=v);var S=$("#rte-affli-no").val();manageInstituteDetails.isNotNullOrEmpty(S)&&(g.RTE_AFFILIATION_NUMBER=S);var b={instituteName:e,branchName:a,letterHeadLine1:m,letterHeadLine2:u,addressLine1:t,addressLine2:n,city:i,state:s,country:d,zipcode:o,landmark:l,email:c,phoneNumber:r,instituteMetadataVariablesMap:g};ajaxClient.post("/institute-management/update-institute",{institutePayload:JSON.stringify(b)},function(e){$("#manage-institute-details-status-modal-container").html(e),$("#manage-institute-details-status-modal").modal("toggle"),manageInstituteDetails.loadManageInstituteDetails()})})},loadManageInstituteDetails:function(){ajaxClient.get("/institute-management/manage-institute-details",function(e){$("#manage-institute-details").html(e)})},isNotNullOrEmpty:function(e){return null!==e&&""!==e}},manageInstituteLogo={dataCache:{},loadManageInstituteLogoPage:function(e){ajaxClient.get("/institute-management/manage-institute-logo-home",function(e){$("#main-content").html(e),manageInstituteLogo.populateUploadedDocuments()})},loadManageInstituteLogo:function(){ajaxClient.get("/institute-management/manage-institute-logo",function(e){$("#manage-institute-uploaded-logo").html(e),manageInstituteLogo.populateUploadedDocuments()})},resetNewDocumentUploadPopup:function(){$("#upload-document-type").val(""),$("#upload-document-file").val(""),$("#upload-document-file-label").text(""),$("#upload-document-name").val(""),registerUploadFileCallback()},populateUploadedDocuments:function(){var e=$("#upload-documents").text().trim();if("None"!==e&&""!==e&&null!=e){var a=JSON.parse(e);if(null!=a&&0!=a.length){for(var t="<br>",n=0,i=0;i<a.length;i++){var s=a[i];if(null!=s.documentId){n%3==0&&(0!=n&&(t+="</div>"),t+='<div class="row">'),n++;var d="Uploaded on : "+getFormattedDate(s.uploadTime);t+='<div class="col-sm-4"> <div class="card bg-light text-center"> <div class="card-header"> <h5> <strong> '+s.documentName+' </strong></h5> </div> <div class="card-body"> <p style="display:none;" class="view-document-id"> '+s.documentId+' </p> <p class="card-text"> Category : '+s.documentTypeDisplayName+' </p> <a href="#" class="btn btn-outline-info download-document">Download </a> <a href="#" class="btn btn-outline-danger delete-document">Delete </a> </div> <div class="card-footer text-muted"> '+d+" </div> </div> </div>"}}t+="</div> <br>",$("#uploaded-documents").html(t),manageInstituteLogo.bindDocumentActions()}else $("#uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>")}else $("#uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>")},bindDocumentActions:function(){$(".download-document").on("click",function(){var e=$(this).parent().find("p.view-document-id").text().trim();window.open(baseURL+"/institute-management/document-download/"+e,"_blank")}),$(".delete-document").on("click",function(){var e=$(this).parent().find("p.view-document-id").text().trim();$("#document-delete-confirm-button").attr("onclick","manageInstituteLogo.deleteDocument('"+e+"')"),$("#document-delete-confirm-modal").modal({backdrop:"static",keyboard:!1})})},uploadDocument:async function(){var e=$("#upload-document-type option:selected").val().trim();if(""!=e){var a;if($("#upload-document-file")[0].files.length>0){var t=$("#upload-document-file")[0].files[0];if((a=await compressFileUtils.compress(t)).size/1024>50)showErrorDialogBox("Size Of document cannot be greater than 50 kb");else{var n=new FormData;n.append("document",a),n.append("documentType",e),n.append("documentName",""),$("#upload-new-document-modal").modal("toggle"),ajaxClient.uploadFile("/institute-management/document-upload",n,function(e){$("#manage-document-upload-status-modal-container").html(e),$("#manage-document_upload_status-modal").modal({backdrop:"static",keyboard:!1}),manageInstituteLogo.loadManageInstituteLogo()})}}else showErrorDialogBox("No file selected. Please choose a document to upload")}else showErrorDialogBox("Document type field is mandatory please fill it then proceed.")},deleteDocument:function(e){ajaxClient.post("/institute-management/document-delete/"+e,{},function(e){$("#manage-document-upload-status-modal-container").html(e),$("#manage-document_upload_status-modal").modal({backdrop:"static",keyboard:!1}),manageInstituteLogo.loadManageInstituteLogo()})}},documentHandler={dataCache:{},registerUploadDocumentCallBack:function(){$(".upload-standard-document").on("click",function(){var e=$(this).parent().find(".manage-standard-session-data-json").text().trim(),a=JSON.parse(e);$("#upload-document-standard-id").text(a.standardRowDetails.standardId),$("#upload-document-section-id").text(a.standardRowDetails.sectionId),$("#manage-class-teachers-list").attr("style","display:none"),$("#academic-year-display").attr("style","display:none"),$("#session-dropdown").attr("style","display:none"),$("#standard-session-upload-document-screen").attr("style","display:block"),$("#upload-document-standard-name").text(a.standardRowDetails.displayNameWithSection),documentHandler.populateUploadedDocuments(a.standardSessionDocuments)}),registerUploadFileCallback()},populateUploadedDocuments:function(e){if(null!=e&&0!=e.length){for(var a="<br>",t=0,n=0;n<e.length;n++){var i=e[n];if(null!=i.documentId){t%3==0&&(0!=t&&(a+="</div>"),a+='<div class="row">'),t++;var s="Uploaded on : "+getFormattedDate(i.uploadTime);a+='<div class="col-sm-4"> <div class="card bg-light text-center"> <div class="card-header"> <h5> <strong> '+i.documentName+' </strong></h5> </div> <div class="card-body"> <p style="display:none;" class="view-document-id"> '+i.documentId+' </p> <a href="#" class="btn btn-outline-info download-standard-document">Download </a> <a href="#" class="btn btn-outline-danger delete-standard-document">Delete </a> </div> <div class="card-footer text-muted"> '+s+" </div> </div> </div>"}}a+="</div> <br>",$("#standard-uploaded-documents").html(a),documentHandler.bindStandardDocumentActions()}else $("#standard-uploaded-documents").html("<br> <h5> No documents uploaded</h5> <br>")},returnToMainScreen:function(){manageClassTeachers.loadManageClassTeacherslist(),$("#manage-class-teachers-list").attr("style","display:block"),$("#academic-year-display").attr("style","display:block"),$("#session-dropdown").attr("style","display:block"),$("#standard-session-upload-document-screen").attr("style","display:none")},resetNewDocumentUploadPopup:function(){$("#upload-document-file").val(""),$("#upload-document-file-label").text(""),$("#upload-standard-document-name").val("")},bindStandardDocumentActions:function(){$(".download-standard-document").on("click",function(){var e=academicSessionHandler.getSelectedSessionId(),a=$("#upload-document-standard-id").text().trim(),t=$("#upload-document-section-id").text().trim(),n=$(this).parent().find("p.view-document-id").text().trim();window.open(baseURL+"/institute-management/standard-document-download/"+a+"/"+n+"?academic_session_id="+e+"&section_id="+t,"_blank")}),$(".delete-standard-document").on("click",function(){var e=$(this).parent().find("p.view-document-id").text().trim();$("#standard-document-delete-confirm-button").attr("onclick","documentHandler.deleteStandardDocument('"+e+"')"),$("#standard-document-delete-confirm-modal").modal({backdrop:"static",keyboard:!1})})},deleteStandardDocument:function(e){var a=academicSessionHandler.getSelectedSessionId(),t=$("#upload-document-standard-id").text().trim(),n=$("#upload-document-section-id").text().trim();ajaxClient.post("/institute-management/standard-document-delete/"+t+"/"+e+"?academic_session_id="+a+"&section_id="+n,{},function(e){if($("#standard-document-status-modal-container").html(e),$("#standard-document-delete-status-modal").modal({backdrop:"static",keyboard:!1}),JSON.parse($("#success-document-delete-response-status").text().trim())){var a=$("#success-document-delete-response").text().trim(),t=JSON.parse(a);documentHandler.populateUploadedDocuments(t)}})},uploadStandardDocument:async function(){var e,a=academicSessionHandler.getSelectedSessionId(),t=$("#upload-document-standard-id").text(),n=$("#upload-document-section-id").text();if($("#upload-document-file")[0].files.length>0){var i=$("#upload-document-file")[0].files[0];if((e=await compressFileUtils.compress(i)).size/1024>500)showErrorDialogBoxWithExistingModalDetails("File size exceeds 500 KB after compression. Please reduce the file size and try uploading again.","#upload-new-document-modal");else{var s=$("#upload-standard-document-name").val();if(""!=s){var d=new FormData;d.append("document",e),d.append("documentName",s),d.append("academicSessionId",a),d.append("standardId",t),d.append("sectionId",n),$("#upload-new-document-modal").modal("toggle"),ajaxClient.uploadFile("/institute-management/standard-document-upload",d,function(e){if($("#standard-document-status-modal-container").html(e),$("#standard-document-upload-status-modal").modal({backdrop:"static",keyboard:!1}),JSON.parse($("#success-document-upload-response-status").text().trim())){var a=$("#success-document-upload-response").text().trim(),t=JSON.parse(a);documentHandler.populateUploadedDocuments(t)}})}else showErrorDialogBoxWithExistingModalDetails("Document name field is mandatory please fill it then proceed","#upload-new-document-modal")}}else showErrorDialogBoxWithExistingModalDetails("No file selected. Please choose a document to upload","#upload-new-document-modal")}},manageStandards={dataCache:{},openEditStandardsModal:function(){manageStandards.refreshStandardsData();var e=readJson("#edit-standards-data");manageStandards.dataCache.standards=e,manageStandards.dataCache.count=0;var a=academicSessionHandler.getSelectedSession();$("#edit-standards-session-display").val(a.displayName),manageStandards.populateStandardsTable(),$("#edit-standards-modal").modal("toggle")},populateStandardsTable:function(){for(var e="",a=manageStandards.dataCache.standards,t=0;t<a.length;t++){manageStandards.dataCache.count+=1;var n=a[t];e+=manageStandards.getStandardRow(n,!1)}e+='<tr id="edit-standards-add-row-bottom" style="display:none;"></tr>',$("#edit-standards-input-div").html(e)},getStandardRow:function(e,a){var t="standard-row-"+manageStandards.dataCache.count,n=e?e.standardId:"",i=e?e.standardName:"",s=e?e.stream:"",d=e?e.level:"";return'<tr class="'+(a?"new-standard-row":"existing-standard-row")+'" id="'+t+"\"><td style=\"vertical-align: middle; text-align: center; white-space: nowrap;\"><button type='button' class='btn btn-sm btn-outline-primary rounded-circle' style='width:24px; height:24px; padding:0; font-size:14px;' title='Insert Class' onclick='manageStandards.insertStandardRow(this)'>+</button></td><td><input type=\"hidden\" class=\"standard-id\" value=\""+n+'"><input type="text" class="form-control standard-name mandatory-field" placeholder="Standard Name..." value="'+i+'" onchange="manageStandards.validateDuplicateStandardStream(this)"></td><td><select class="form-control standard-stream mandatory-field" onchange="manageStandards.validateDuplicateStandardStream(this)">'+manageStandards.getStreamOptions(s)+'</select></td><td><input type="number" class="form-control standard-level mandatory-field" placeholder="Level..." value="'+d+'" onchange="manageStandards.validateDuplicateLevel(this)"></td><td style="vertical-align: middle; text-align: center; white-space: nowrap;">'+(a?"<button type='button' class='btn btn-sm btn-outline-danger rounded-circle' style='width:24px; height:24px; padding:0; font-size:16px;' title='Delete Class' onclick='manageStandards.deleteStandardRow(this)'>&times;</button>":"")+"</td></tr>"},insertStandardRow:function(e){manageStandards.dataCache.count+=1;var a=manageStandards.getStandardRow(null,!0);$(e).closest("tr").after(a),manageStandards.adjustLevels()},adjustLevels:function(){var e=1;$("#edit-standards-input-div tr").not("#edit-standards-add-row-bottom").each(function(){$(this).find(".standard-level").val(e),e++})},getStreamOptions:function(e){for(var a=[{value:"SCIENCE",display:"Science"},{value:"COMMERCE",display:"Commerce"},{value:"ARTS",display:"Arts"},{value:"AGRICULTURE",display:"Agriculture"},{value:"MATHS",display:"Maths"},{value:"BIOLOGY",display:"Biology"},{value:"HUMANITIES",display:"Humanities"},{value:"HINDI",display:"Hindi"},{value:"ENGLISH",display:"English"},{value:"PCB",display:"PCB"},{value:"PCM",display:"PCM"},{value:"JEE",display:"JEE"},{value:"NEET",display:"NEET"},{value:"NDA",display:"NDA"},{value:"NA",display:"NA"}],t='<option value="">Select Stream</option>',n=0;n<a.length;n++){var i=a[n].value===e?"selected":"";t+='<option value="'+a[n].value+'" '+i+">"+a[n].display+"</option>"}return t},deleteStandardRow:function(e){$(e).closest("tr").remove(),manageStandards.adjustLevels()},validateDuplicateStandardStream:function(e){var a=$(e).closest("tr"),t=a.find(".standard-name").val().trim(),n=a.find(".standard-stream").val().trim();if(""!==t&&""!==n){var i=!1;return $("#edit-standards-input-div tr").not(a).each(function(){var e=$(this).find(".standard-name").val(),a=$(this).find(".standard-stream").val();if(e&&a&&e.trim()===t&&a.trim()===n)return i=!0,!1}),i?(alert("Duplicate Standard Name + Stream combination is not allowed"),void $(e).val("")):void 0}},validateDuplicateLevel:function(e){var a=$(e).closest("tr"),t=$(e).val().trim();if(""!==t){var n=!1;return $("#edit-standards-input-div tr").not(a).each(function(){var e=$(this).find(".standard-level").val();if(e&&e.trim()===t)return n=!0,!1}),n?(alert("Duplicate Level is not allowed"),void $(e).val("")):void 0}},updateStandards:function(){if(validateMandatoryFields($("#edit-standards-modal")))alert("Please fill all mandatory fields");else{var e=[];if($("#edit-standards-input-div tr").not("#edit-standards-add-row-bottom").each(function(){var a=$(this).find(".standard-id").val().trim(),t=$(this).find(".standard-name").val().trim(),n=$(this).find(".standard-stream").val().trim(),i=$(this).find(".standard-level").val().trim();if(t&&n&&i){var s={standardId:""===a?null:a,standardName:t,stream:n,level:parseInt(i)};e.push(s)}}),0!==e.length){$("#edit-standards-modal").modal("toggle");var a=academicSessionHandler.getSelectedSessionId();ajaxClient.post("/institute-management/update-standards/"+a,{standardsData:JSON.stringify(e)},function(e){$("#manage-sections\\.status-modal-container").html(e),$("#manage-sections\\.status-modal").modal({backdrop:"static",keyboard:!1}),manageSections.loadManageSectionslist(),manageStandards.refreshStandardsData()})}else alert("Please add at least one standard")}},refreshStandardsData:function(){var e=academicSessionHandler.getSelectedSessionId();ajaxClient.get("/institute-management/get-standards-json/"+e,function(e){var a=e;console.log("Updated standards data:",a),a&&($("#edit-standards-data").text(JSON.stringify(a)),manageStandards.dataCache.standards=a,manageStandards.dataCache.count=0)})}};